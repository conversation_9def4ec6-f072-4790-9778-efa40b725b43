# Job Management System

This document describes the PostgreSQL-based job management system for Excel file evaluation and other long-running tasks.

## Overview

The job management system provides:
- **Job Tracking**: Create, monitor, and manage evaluation jobs
- **Progress Monitoring**: Real-time progress updates and status checking
- **Job Control**: Cancel running jobs, view logs, and download results
- **History Management**: Complete job history with filtering and pagination
- **Performance Metrics**: Track job performance and resource usage

## Architecture

### Components

1. **Database Models** (`app/job_management/models.py`)
   - `EvaluationJob`: Main job tracking table
   - `JobLog`: Detailed job execution logs
   - `JobMetrics`: Performance metrics and statistics

2. **Database Service** (`app/job_management/database.py`)
   - PostgreSQL connection management
   - SQLite fallback for development
   - Session management and health checks

3. **Job Service** (`app/job_management/job_service.py`)
   - Job lifecycle management
   - Progress tracking and updates
   - Cancellation and error handling

4. **API Endpoints**
   - `app/job_management/api.py`: General job management APIs
   - `app/job_management/excel_api.py`: Excel-specific job APIs

5. **Enhanced Evaluator** (`app/job_management/excel_evaluator_with_jobs.py`)
   - Excel evaluator with job tracking integration
   - Progress reporting and cancellation support

## Database Setup

### PostgreSQL (Recommended)

1. **Install PostgreSQL**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install postgresql postgresql-contrib
   
   # macOS
   brew install postgresql
   ```

2. **Create Database**
   ```sql
   CREATE DATABASE llm_translation_qe;
   CREATE USER qe_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE llm_translation_qe TO qe_user;
   ```

3. **Set Environment Variable**
   ```bash
   export DATABASE_URL="postgresql://qe_user:your_password@localhost:5432/llm_translation_qe"
   ```

### SQLite Fallback

If PostgreSQL is not available, the system automatically falls back to SQLite:
- Database file: `./job_management.db`
- No additional setup required
- Suitable for development and testing

## API Endpoints

### Job Management APIs

#### 1. List Jobs
```
GET /jobs/
```

**Parameters:**
- `status`: Filter by job status (pending, running, completed, failed, cancelled)
- `job_type`: Filter by job type (excel_evaluation, gle_workflow, single_submission)
- `user_id`: Filter by user ID
- `limit`: Maximum results (1-100, default: 50)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "jobs": [...],
  "total": 150,
  "limit": 50,
  "offset": 0
}
```

#### 2. Get Job Details
```
GET /jobs/{job_id}
```

**Response:**
```json
{
  "id": 1,
  "job_id": "uuid-string",
  "job_type": "excel_evaluation",
  "status": "running",
  "created_at": "2024-01-01T10:00:00Z",
  "started_at": "2024-01-01T10:01:00Z",
  "filename": "test.xlsx",
  "total_segments": 100,
  "processed_segments": 45,
  "passed_segments": 42,
  "progress_percentage": 45.0,
  "current_step": "Evaluating segment 46/100",
  "model_name": "google/gemini-2.0-flash",
  "temperature": 0.0,
  "use_rag": true,
  "rag_collection": "paypal_glossary"
}
```

#### 3. Get Job Status (Lightweight)
```
GET /jobs/{job_id}/status
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "running",
  "progress_percentage": 45.0,
  "current_step": "Evaluating segment 46/100",
  "processed_segments": 45,
  "total_segments": 100,
  "is_active": true,
  "can_cancel": true
}
```

#### 4. Cancel Job
```
POST /jobs/{job_id}/cancel
```

**Request Body:**
```json
{
  "reason": "User requested cancellation"
}
```

#### 5. Get Job Logs
```
GET /jobs/{job_id}/logs
```

**Parameters:**
- `level`: Filter by log level (INFO, WARNING, ERROR, DEBUG)
- `limit`: Maximum logs (1-1000, default: 100)
- `offset`: Pagination offset

#### 6. Get Job Metrics
```
GET /jobs/{job_id}/metrics
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "total_duration_seconds": 120.5,
  "segments_per_second": 0.83,
  "peak_memory_mb": 256.7,
  "cpu_usage_percent": 45.2
}
```

#### 7. Download Job Result
```
GET /jobs/{job_id}/download
```

Returns the processed Excel file for completed jobs.

#### 8. Delete Job
```
DELETE /jobs/{job_id}
```

Deletes completed/failed/cancelled jobs and all associated data.

#### 9. Job Statistics
```
GET /jobs/stats/summary
```

**Response:**
```json
{
  "total_jobs": 150,
  "active_jobs": 3,
  "status_counts": {
    "pending": 1,
    "running": 2,
    "completed": 140,
    "failed": 5,
    "cancelled": 2
  },
  "type_counts": {
    "excel_evaluation": 145,
    "gle_workflow": 3,
    "single_submission": 2
  }
}
```

### Excel Job APIs

#### 1. Start Excel Evaluation
```
POST /excel-jobs/evaluate
```

**Form Data:**
- `file`: Excel file (.xlsx or .xls)
- `prompt_template`: JSON string of prompt template
- `rag_configs`: JSON string of RAG configurations (optional)
- `user_id`: User identifier (optional)

**Response:**
```json
{
  "job_id": "uuid-string",
  "message": "Excel evaluation job started successfully",
  "status_url": "/jobs/{job_id}/status",
  "download_url": "/jobs/{job_id}/download"
}
```

#### 2. Get Active Excel Jobs
```
GET /excel-jobs/active
```

**Parameters:**
- `user_id`: Filter by user ID (optional)

#### 3. Get Excel Job History
```
GET /excel-jobs/history
```

**Parameters:**
- `user_id`: Filter by user ID (optional)
- `limit`: Maximum results (default: 50)
- `offset`: Pagination offset (default: 0)

#### 4. Cancel Excel Job
```
POST /excel-jobs/{job_id}/cancel
```

## Usage Examples

### 1. Start Excel Evaluation Job

```python
import requests

# Prepare files and data
files = {'file': open('translation_data.xlsx', 'rb')}
data = {
    'prompt_template': json.dumps({
        "template_messages": [...],
        "llm_configuration": {...},
        "output_schema": [...]
    }),
    'rag_configs': json.dumps([...]),
    'user_id': 'user123'
}

# Start job
response = requests.post(
    'http://localhost:8080/excel-jobs/evaluate',
    files=files,
    data=data
)

job_info = response.json()
job_id = job_info['job_id']
print(f"Job started: {job_id}")
```

### 2. Monitor Job Progress

```python
import time

job_id = "your-job-id"

while True:
    response = requests.get(f'http://localhost:8080/jobs/{job_id}/status')
    status = response.json()
    
    print(f"Status: {status['status']}")
    print(f"Progress: {status['progress_percentage']}%")
    print(f"Step: {status['current_step']}")
    
    if status['status'] in ['completed', 'failed', 'cancelled']:
        break
    
    time.sleep(5)  # Check every 5 seconds
```

### 3. Download Results

```python
if status['status'] == 'completed':
    response = requests.get(f'http://localhost:8080/jobs/{job_id}/download')
    
    with open('evaluated_file.xlsx', 'wb') as f:
        f.write(response.content)
    
    print("Results downloaded successfully")
```

### 4. Cancel Job

```python
response = requests.post(
    f'http://localhost:8080/jobs/{job_id}/cancel',
    json={'reason': 'User requested cancellation'}
)

print(response.json()['message'])
```

### 5. View Job Logs

```python
response = requests.get(f'http://localhost:8080/jobs/{job_id}/logs')
logs = response.json()

for log in logs['logs']:
    print(f"[{log['timestamp']}] {log['level']}: {log['message']}")
```

## Job Lifecycle

1. **PENDING**: Job created, waiting to start
2. **RUNNING**: Job actively processing
3. **COMPLETED**: Job finished successfully
4. **FAILED**: Job encountered an error
5. **CANCELLED**: Job was cancelled by user

## Error Handling

The system includes comprehensive error handling:

- **Database Errors**: Automatic fallback to SQLite
- **Job Failures**: Detailed error logging and recovery
- **Cancellation**: Graceful job termination
- **Resource Management**: Memory and CPU monitoring

## Performance Considerations

- **Concurrent Jobs**: Multiple jobs can run simultaneously
- **Resource Monitoring**: Track memory and CPU usage
- **Progress Updates**: Efficient progress tracking without performance impact
- **Database Optimization**: Indexed queries for fast job retrieval

## Monitoring and Maintenance

### Health Checks

Monitor system health through:
- Job statistics endpoint
- Database connection tests
- Active job monitoring

### Cleanup

Regularly clean up old jobs:
```python
# Delete completed jobs older than 30 days
DELETE FROM evaluation_jobs 
WHERE status = 'completed' 
AND completed_at < NOW() - INTERVAL '30 days';
```

### Backup

Backup job data regularly:
```bash
pg_dump llm_translation_qe > job_backup.sql
```

## Integration with Existing APIs

The job management system **does not modify** existing APIs. Instead, it provides:

- **Enhanced Excel evaluation** with job tracking
- **Parallel job processing** alongside existing endpoints
- **Optional integration** - existing APIs continue to work unchanged

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify DATABASE_URL environment variable
   - System will fall back to SQLite automatically

2. **Job Stuck in RUNNING**
   - Check application logs
   - Use cancel endpoint to stop job
   - Restart application if needed

3. **High Memory Usage**
   - Monitor job metrics
   - Cancel resource-intensive jobs
   - Adjust concurrent job limits

4. **Slow Job Processing**
   - Check LLM service availability
   - Monitor RAG service performance
   - Review job logs for bottlenecks

### Debug Mode

Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

1. **Job Scheduling**: Cron-like job scheduling
2. **Job Dependencies**: Chain jobs together
3. **Resource Limits**: CPU and memory limits per job
4. **Notifications**: Email/webhook notifications
5. **Job Templates**: Reusable job configurations
6. **Batch Operations**: Process multiple files in one job
7. **Priority Queues**: High-priority job processing
