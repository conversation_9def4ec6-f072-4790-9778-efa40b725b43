<?xml version="1.0" encoding="UTF-8"?><xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:gs4tr="http://www.gs4tr.org/schema/xliff-ext"><file gs4tr:xliff-module-version="6.16.0" original="DE_en_personalized_guidance.properties" gs4tr:system-attributes="&lt;attrs pd.xliff.id=&quot;1823571&quot;/&gt;" gs4tr:uuid="92cc5d62-85a3-43a5-a585-2beb8eec7a37" gs4tr:target-encoding="UTF-8" gs4tr:total-segment-count="8" gs4tr:target-eol="unix" gs4tr:md5checksum="0b605e4f6f2bf81e615059861b13e745" datatype="x-regexp" source-language="en" target-language="zh" gs4tr:translated-segment-count="8" gs4tr:simplifier="fontformat"><header><skl><internal-file>emptyMsg=￼{paragraph-1-1}￼
dismiss=￼{paragraph-1-2}￼
remindLater=￼{paragraph-1-3}￼
required=￼{paragraph-1-4}￼
urgent=￼{paragraph-1-5}￼
confirmEmailToastMessage=￼{paragraph-1-6}￼
confirmEmailToastMessageFailure=￼{paragraph-1-7}￼
confirmEmailToastMessageCooldown=￼{paragraph-1-8}￼
</internal-file></skl><phase-group><phase phase-name="API_QE-1" process-name="Review" contact-name="paypal_api_qe" tool-id="PD" date="2025-06-05T09:06:09Z"/></phase-group><tool tool-id="PD" tool-name="ProjectDirector" tool-version="10.2.0"/></header><body><group id="page-1" restype="x-page" >
<group id="paragraph-1-1" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;emptyMsg&quot; property-name=&quot;emptyMsg&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-1-1" gs4tr:wordcount="7" gs4tr:editStatus="leveraged" >
<source>You're all caught up for today!</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T102540Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="x-in-context-match" state="translated">您今日没有待处理事项！</target>
</trans-unit>
</group>
<group id="paragraph-1-2" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;dismiss&quot; property-name=&quot;dismiss&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-2-1" gs4tr:wordcount="1" gs4tr:editStatus="leveraged" >
<source>Dismiss</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230802T022224Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="x-in-context-match" state="translated">关闭</target>
</trans-unit>
</group>
<group id="paragraph-1-3" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;remindLater&quot; property-name=&quot;remindLater&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-3-1" gs4tr:wordcount="3" gs4tr:editStatus="leveraged" >
<source>Remind me later</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T115336Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="exact-match" state="needs-translation">以后再提醒我</target>
</trans-unit>
</group>
<group id="paragraph-1-4" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;required&quot; property-name=&quot;required&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-4-1" gs4tr:wordcount="1" gs4tr:editStatus="leveraged" >
<source>Required</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T095647Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="exact-match" state="needs-translation">必需</target>
</trans-unit>
</group>
<group id="paragraph-1-5" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;urgent&quot; property-name=&quot;urgent&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-5-1" gs4tr:wordcount="1" gs4tr:editStatus="leveraged" >
<source>Urgent</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T113616Z&quot;/&gt;" gs4tr:score="99" gs4tr:repscore="0" gs4tr:engine="103" state="needs-translation" state-qualifier="fuzzy-match">紧急</target>
</trans-unit>
</group>
<group id="paragraph-1-6" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;confirmEmailToastMessage&quot; property-name=&quot;confirmEmailToastMessage&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-6-1" gs4tr:wordcount="9" gs4tr:editStatus="leveraged" >
<source>Sent! Check your inbox for an <NAME_EMAIL>.</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T102540Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="exact-match" state="needs-translation">已发送！请查看您的收件箱，找到******************发送的电子邮件。</target>
</trans-unit>
</group>
<group id="paragraph-1-7" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;confirmEmailToastMessageFailure&quot; property-name=&quot;confirmEmailToastMessageFailure&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-7-1" gs4tr:wordcount="15" gs4tr:editStatus="leveraged" >
<source>Something went wrong — we couldn't resend the link. Refresh the page and try again.</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T102540Z&quot;/&gt;" gs4tr:score="98" gs4tr:repscore="0" gs4tr:engine="103" state="needs-translation" state-qualifier="fuzzy-match">出现错误——我们无法重新发送链接。请刷新页面，然后重试。</target>
</trans-unit>
</group>
<group id="paragraph-1-8" restype="x-paragraph" gs4tr:user-attributes="&lt;attrs string-id=&quot;confirmEmailToastMessageCooldown&quot; property-name=&quot;confirmEmailToastMessageCooldown&quot;/&gt;" gs4tr:segmented="true" >
<trans-unit id="1-8-1" gs4tr:wordcount="19" gs4tr:editStatus="leveraged" >
<source>Check your inbox or spam for an <NAME_EMAIL>. Didn't get it? You can resend in <ph ctype="x-unknown" id="1">{cooldownSeconds}</ph> seconds.</source>
<target gs4tr:seginfo="&lt;root username=&quot;PayPalTPTImport&quot; timestamp=&quot;20230811T102540Z&quot;/&gt;" gs4tr:score="100" gs4tr:repscore="0" gs4tr:engine="103" state-qualifier="exact-match" state="needs-translation" gs4tr:replacement="1">查看您的收件箱或垃圾邮件箱，找到******************发送的电子邮件。没有收到邮件？您可以在<ph ctype="x-unknown" id="1">{cooldownSeconds}</ph>秒后重新发送。</target>
</trans-unit>
</group>
</group></body></file></xliff>