# CosmosAI LLM Integration

This document describes the integration with PayPal's CosmosAI platform, which provides access to multiple LLM providers through an OpenAI-compatible API.

## Overview

The CosmosAI integration provides access to 14 different models from various providers:

### Supported Models

1. **Claude Models (Anthropic)**
   - `claude-3-7-sonnet-20250219`
   - `claude-3-5-haiku-********`
   - `claude-sonnet-4-20250514`

2. **GPT Models (OpenAI)**
   - `gpt-4.1`
   - `gpt-4o`
   - `gpt-4o-mini`

3. **Llama Models (Meta)**
   - `llama33-70b`
   - `llama31-8b`

4. **DeepSeek Models**
   - `deepseek-r1-distill-llama-70b`
   - `deepseek-r1-distill-qwen-32b`

5. **Qwen Models**
   - `qwen25-coder-32b`

6. **Gemini Models (Google)**
   - `gemini-2.5-pro-preview-03-25`
   - `gemini-2.0-flash`
   - `gemini-2.0-flash-lite`

## Configuration

### Environment Variables

Set your CosmosAI API key as an environment variable:

```bash
export COSMOSAI_API_KEY="your_actual_api_key_here"
```

### Config File

Add the CosmosAI configuration to `app/config.ini`:

```ini
[cosmosai]
api_key = <LLM_API_KEY>
base_url = https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1
```

## Usage

### Basic Usage

```python
from app.llm.llm_factory import LLMFactory

# Create LLM instance
llm = LLMFactory.build_llm("gpt-4o-mini")

# Prepare messages
messages = [
    {"role": "user", "content": "Hello, how are you?"}
]

# LLM configuration
config = {
    "temperature": 0.7,
    "max_tokens": 150,
    "top_p": 0.9
}

# Sync chat
response = llm.chat(messages, config)
print(response)

# Async chat
response = await llm.async_chat(messages, config)
print(response)
```

### Translation Quality Evaluation

```python
# Use Claude for high-quality evaluation
llm = LLMFactory.build_llm("claude-3-5-haiku-********")

messages = [
    {
        "role": "system",
        "content": "You are a professional translation quality evaluator."
    },
    {
        "role": "user",
        "content": """
        Evaluate this translation:
        Source: "Click here to access your account"
        Target: "Haga clic aquí para acceder a su cuenta"
        
        Provide a score from 1-100 and explanation.
        """
    }
]

config = {"temperature": 0.0, "max_tokens": 200}
evaluation = await llm.async_chat(messages, config)
```

### Structured Output

```python
# Use GPT models for structured output
llm = LLMFactory.build_llm("gpt-4o")

messages = [
    {
        "role": "user",
        "content": "Evaluate this translation and respond in JSON format."
    }
]

# Define JSON schema
schema = {
    "type": "object",
    "properties": {
        "score": {"type": "number", "description": "Quality score 1-100"},
        "category": {"type": "string", "description": "Error category"},
        "comment": {"type": "string", "description": "Brief explanation"}
    },
    "required": ["score", "category", "comment"]
}

config = {"temperature": 0.0, "max_tokens": 150}
result = llm.chat(messages, config, schema)

# Result will be a parsed dictionary
print(f"Score: {result['score']}")
print(f"Category: {result['category']}")
```

## API Reference

### CosmosAILLM Class

#### Constructor

```python
CosmosAILLM(model_name: str)
```

- `model_name`: One of the supported model IDs

#### Methods

##### chat()

```python
def chat(
    messages: List[Dict[str, str]],
    llm_configuration: Optional[Dict[str, Any]] = None,
    structured_output_schema: Optional[Dict] = None,
) -> str | Dict
```

Synchronous chat completion.

**Parameters:**
- `messages`: List of message dictionaries with 'role' and 'content'
- `llm_configuration`: Configuration parameters (temperature, max_tokens, etc.)
- `structured_output_schema`: JSON schema for structured output

**Returns:**
- String response or parsed dictionary (if structured output)

##### async_chat()

```python
async def async_chat(
    messages: List[Dict[str, str]],
    llm_configuration: Optional[Dict[str, Any]] = None,
    structured_output_schema: Optional[Dict] = None,
) -> str | Dict
```

Asynchronous chat completion with same parameters as `chat()`.

##### get_available_models()

```python
def get_available_models() -> List[str]
```

Returns list of available model IDs from CosmosAI.

##### test_model()

```python
def test_model(test_message: str = "Hi, who are you?") -> bool
```

Tests if the model is working correctly.

### Configuration Parameters

#### LLM Configuration

```python
llm_config = {
    "temperature": 0.7,        # Randomness (0.0-2.0)
    "max_tokens": 1024,        # Maximum response length
    "top_p": 0.9,             # Nucleus sampling
    "frequency_penalty": 0.0,  # Frequency penalty
    "presence_penalty": 0.0    # Presence penalty
}
```

#### Structured Output Schema

```python
schema = {
    "type": "object",
    "properties": {
        "field_name": {
            "type": "string|number|boolean|array|object",
            "description": "Field description"
        }
    },
    "required": ["field1", "field2"]
}
```

## Model Recommendations

### For Translation Quality Evaluation

1. **High Accuracy**: `claude-3-7-sonnet-20250219`, `gpt-4o`
2. **Balanced**: `claude-3-5-haiku-********`, `gpt-4o-mini`
3. **Fast/Cost-effective**: `llama31-8b`, `gemini-2.0-flash-lite`

### For Structured Output

1. **Best Support**: `gpt-4o`, `gpt-4o-mini`
2. **Alternative**: `claude-3-5-haiku-********`

### For Multilingual Tasks

1. **Best**: `gemini-2.0-flash`, `claude-3-7-sonnet-20250219`
2. **Good**: `gpt-4o`, `llama33-70b`

## Error Handling

The integration includes comprehensive error handling:

```python
try:
    response = llm.chat(messages, config)
except Exception as e:
    print(f"Error: {e}")
    # Handle error appropriately
```

Common errors:
- **Authentication**: Invalid API key
- **Rate Limiting**: Too many requests
- **Model Unavailable**: Model temporarily down
- **Invalid Input**: Malformed messages or config

## Testing

### Run Tests

```bash
# Test the integration
python test_new_cosmosai_llm.py

# Run usage examples
python cosmosai_usage_example.py
```

### Test Individual Models

```python
from app.llm.new_cosmosai_llm import CosmosAILLM

# Test a specific model
llm = CosmosAILLM("gpt-4o-mini")
is_working = llm.test_model()
print(f"Model working: {is_working}")
```

## Performance Considerations

### Model Speed (approximate)

1. **Fastest**: `gemini-2.0-flash-lite`, `gpt-4o-mini`
2. **Fast**: `llama31-8b`, `claude-3-5-haiku-********`
3. **Medium**: `gpt-4o`, `gemini-2.0-flash`
4. **Slower**: `claude-3-7-sonnet-20250219`, `llama33-70b`

### Cost Optimization

1. Use smaller models for simple tasks
2. Set appropriate `max_tokens` limits
3. Use lower `temperature` for consistent results
4. Cache responses when possible

## Integration with Translation QE

### Example Integration

```python
from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.schemas import PromptTemplate, LLMConfiguration

# Create prompt template with CosmosAI model
llm_config = LLMConfiguration(
    model_name="claude-3-5-haiku-********",
    temperature=0.0,
    top_p=0.5
)

prompt_template = PromptTemplate(
    template_messages=[...],
    llm_configuration=llm_config,
    output_schema=[...]
)

# Use in translation evaluation
evaluator = LLMTranslationEvaluator()
result = await evaluator.call_llm(prompt_template, rag_configs)
```

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   ```
   Error: CosmosAI API key not configured
   ```
   Solution: Set `COSMOSAI_API_KEY` environment variable

2. **Model Not Supported**
   ```
   Error: Model xyz is not supported
   ```
   Solution: Use one of the supported model IDs

3. **Connection Error**
   ```
   Error: Connection failed
   ```
   Solution: Check network connectivity and API endpoint

4. **Rate Limiting**
   ```
   Error: Rate limit exceeded
   ```
   Solution: Implement retry logic with backoff

### Debug Mode

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Migration from Legacy CosmosAI

If you're migrating from the legacy CosmosAI implementation:

### Old Format
```python
# Legacy format
llm = LLMFactory.build_llm("deepseek-ai/DeepSeek-R1-Distill-Llama-70B")
```

### New Format
```python
# New format
llm = LLMFactory.build_llm("deepseek-r1-distill-llama-70b")
```

The new implementation provides:
- Simpler model names
- OpenAI-compatible API
- Better error handling
- Structured output support
- Async support

## Future Enhancements

1. **Streaming Support**: Real-time response streaming
2. **Function Calling**: Tool use capabilities
3. **Image Input**: Vision model support
4. **Batch Processing**: Multiple requests in one call
5. **Custom Fine-tuning**: Model customization
