"""
Test script for job management system.
"""
import asyncio
import json
import time
from datetime import datetime

import requests


class JobManagementTester:
    """Test class for job management APIs."""
    
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
    
    def test_job_stats(self):
        """Test job statistics endpoint."""
        print("=" * 50)
        print("TESTING JOB STATISTICS")
        print("=" * 50)
        
        try:
            response = requests.get(f"{self.base_url}/jobs/stats/summary")
            if response.status_code == 200:
                stats = response.json()
                print("✓ Job statistics retrieved successfully:")
                print(f"  Total jobs: {stats['total_jobs']}")
                print(f"  Active jobs: {stats['active_jobs']}")
                print(f"  Status counts: {stats['status_counts']}")
                print(f"  Type counts: {stats['type_counts']}")
            else:
                print(f"✗ Failed to get job statistics: {response.status_code}")
                print(f"  Response: {response.text}")
        except Exception as e:
            print(f"✗ Error testing job statistics: {e}")
    
    def test_list_jobs(self):
        """Test job listing endpoint."""
        print("\n" + "=" * 50)
        print("TESTING JOB LISTING")
        print("=" * 50)
        
        try:
            # Test basic listing
            response = requests.get(f"{self.base_url}/jobs/")
            if response.status_code == 200:
                job_list = response.json()
                print("✓ Job listing retrieved successfully:")
                print(f"  Total jobs: {job_list['total']}")
                print(f"  Jobs returned: {len(job_list['jobs'])}")
                
                if job_list['jobs']:
                    job = job_list['jobs'][0]
                    print(f"  Sample job: {job['job_id']} ({job['status']})")
            else:
                print(f"✗ Failed to list jobs: {response.status_code}")
                print(f"  Response: {response.text}")
            
            # Test filtering
            print("\nTesting job filtering...")
            response = requests.get(f"{self.base_url}/jobs/?status=completed&limit=5")
            if response.status_code == 200:
                filtered_jobs = response.json()
                print(f"✓ Filtered jobs (completed): {len(filtered_jobs['jobs'])}")
            else:
                print(f"✗ Failed to filter jobs: {response.status_code}")
                
        except Exception as e:
            print(f"✗ Error testing job listing: {e}")
    
    def test_excel_job_endpoints(self):
        """Test Excel job specific endpoints."""
        print("\n" + "=" * 50)
        print("TESTING EXCEL JOB ENDPOINTS")
        print("=" * 50)
        
        try:
            # Test active Excel jobs
            response = requests.get(f"{self.base_url}/excel-jobs/active")
            if response.status_code == 200:
                active_jobs = response.json()
                print("✓ Active Excel jobs retrieved:")
                print(f"  Active jobs count: {active_jobs['active_jobs']}")
            else:
                print(f"✗ Failed to get active Excel jobs: {response.status_code}")
            
            # Test Excel job history
            response = requests.get(f"{self.base_url}/excel-jobs/history?limit=10")
            if response.status_code == 200:
                history = response.json()
                print("✓ Excel job history retrieved:")
                print(f"  Total jobs: {history['total']}")
                print(f"  Jobs returned: {len(history['jobs'])}")
            else:
                print(f"✗ Failed to get Excel job history: {response.status_code}")
                
        except Exception as e:
            print(f"✗ Error testing Excel job endpoints: {e}")
    
    def test_job_operations(self, job_id=None):
        """Test job operations like get, cancel, logs."""
        print("\n" + "=" * 50)
        print("TESTING JOB OPERATIONS")
        print("=" * 50)
        
        if not job_id:
            # Try to get a job ID from the job list
            try:
                response = requests.get(f"{self.base_url}/jobs/?limit=1")
                if response.status_code == 200:
                    jobs = response.json()['jobs']
                    if jobs:
                        job_id = jobs[0]['job_id']
                        print(f"Using job ID: {job_id}")
                    else:
                        print("No jobs found to test with")
                        return
                else:
                    print("Could not get job list for testing")
                    return
            except Exception as e:
                print(f"Error getting job for testing: {e}")
                return
        
        try:
            # Test get job details
            response = requests.get(f"{self.base_url}/jobs/{job_id}")
            if response.status_code == 200:
                job = response.json()
                print("✓ Job details retrieved:")
                print(f"  Job ID: {job['job_id']}")
                print(f"  Status: {job['status']}")
                print(f"  Progress: {job['progress_percentage']}%")
            else:
                print(f"✗ Failed to get job details: {response.status_code}")
            
            # Test get job status
            response = requests.get(f"{self.base_url}/jobs/{job_id}/status")
            if response.status_code == 200:
                status = response.json()
                print("✓ Job status retrieved:")
                print(f"  Status: {status['status']}")
                print(f"  Can cancel: {status['can_cancel']}")
            else:
                print(f"✗ Failed to get job status: {response.status_code}")
            
            # Test get job logs
            response = requests.get(f"{self.base_url}/jobs/{job_id}/logs?limit=5")
            if response.status_code == 200:
                logs = response.json()
                print("✓ Job logs retrieved:")
                print(f"  Total logs: {logs['total']}")
                print(f"  Logs returned: {len(logs['logs'])}")
            else:
                print(f"✗ Failed to get job logs: {response.status_code}")
            
            # Test get job metrics (might not exist for all jobs)
            response = requests.get(f"{self.base_url}/jobs/{job_id}/metrics")
            if response.status_code == 200:
                metrics = response.json()
                print("✓ Job metrics retrieved:")
                print(f"  Duration: {metrics.get('total_duration_seconds', 'N/A')}s")
            elif response.status_code == 404:
                print("ℹ No metrics available for this job (expected for some jobs)")
            else:
                print(f"✗ Failed to get job metrics: {response.status_code}")
                
        except Exception as e:
            print(f"✗ Error testing job operations: {e}")
    
    def test_database_connection(self):
        """Test database connection through health check."""
        print("\n" + "=" * 50)
        print("TESTING DATABASE CONNECTION")
        print("=" * 50)
        
        try:
            # This would require a health check endpoint
            # For now, we'll test by trying to get job stats
            response = requests.get(f"{self.base_url}/jobs/stats/summary")
            if response.status_code == 200:
                print("✓ Database connection working (job stats accessible)")
            else:
                print(f"✗ Database connection issue: {response.status_code}")
        except Exception as e:
            print(f"✗ Error testing database connection: {e}")
    
    def demo_excel_evaluation_workflow(self):
        """Demonstrate the Excel evaluation workflow."""
        print("\n" + "=" * 50)
        print("EXCEL EVALUATION WORKFLOW DEMO")
        print("=" * 50)
        
        print("This demo shows how to use the job management system:")
        print()
        print("1. Upload Excel file for evaluation:")
        print("   POST /excel-jobs/evaluate")
        print("   - Upload file and configuration")
        print("   - Returns job_id immediately")
        print()
        print("2. Monitor job progress:")
        print("   GET /jobs/{job_id}/status")
        print("   - Check progress percentage")
        print("   - See current processing step")
        print()
        print("3. View job logs:")
        print("   GET /jobs/{job_id}/logs")
        print("   - See detailed processing logs")
        print("   - Monitor for errors")
        print()
        print("4. Cancel job if needed:")
        print("   POST /jobs/{job_id}/cancel")
        print("   - Stop processing")
        print("   - Provide cancellation reason")
        print()
        print("5. Download results:")
        print("   GET /jobs/{job_id}/download")
        print("   - Download evaluated Excel file")
        print("   - Available when status = 'completed'")
        print()
        print("6. View job history:")
        print("   GET /excel-jobs/history")
        print("   - See all past evaluations")
        print("   - Filter by user, status, etc.")
    
    def run_all_tests(self):
        """Run all tests."""
        print("JOB MANAGEMENT SYSTEM TESTS")
        print("=" * 60)
        print(f"Testing against: {self.base_url}")
        print(f"Timestamp: {datetime.now()}")
        
        self.test_database_connection()
        self.test_job_stats()
        self.test_list_jobs()
        self.test_excel_job_endpoints()
        self.test_job_operations()
        self.demo_excel_evaluation_workflow()
        
        print("\n" + "=" * 60)
        print("TESTS COMPLETED")
        print("=" * 60)


def main():
    """Main test function."""
    tester = JobManagementTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
