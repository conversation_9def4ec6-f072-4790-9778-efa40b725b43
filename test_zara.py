import requests
import openpyxl
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Config
API_URL = "http://*************:8082/llm-translation-qe/call-llm"
RAG_SEARCH_URL = "http://*************:8082/ai-localization/rag/search"
COLLECTION = ""
MODEL_NAME = "google/gemini-2.0-flash"
TARGET_LANG = ""
TIMEOUT = 300
BATCH_SIZE_RAG = 30
GROUP_SIZE = 10
MAX_WORKERS = 5

INPUT_XLSX =
OUTPUT_XLSX =


def read_workbook(path: str):
    wb = openpyxl.load_workbook(path)
    ws = wb.active
    items = []
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            if isinstance(cell.value, str) and cell.value.strip():
                items.append((cell.row, cell.column, cell.value))
    return wb, ws, items


def batched_rag_search(texts, limit=10, batch_size=BATCH_SIZE_RAG):
    all_contexts = []
    for i in range(0, len(texts), batch_size):
        chunk = texts[i: i + batch_size]
        print(f"RAG chunk {i // batch_size + 1}: {len(chunk)} texts")
        resp = requests.post(
            RAG_SEARCH_URL,
            json={"collection_name": COLLECTION, "texts": chunk, "limit": limit},
            timeout=TIMEOUT
        )
        resp.raise_for_status()
        for hits in resp.json()["search_results"]:
            context = "\n".join(h["text_for_retrieval"] for h in hits)
            all_contexts.append(context)
    return all_contexts


def translate_batch(batch):
    parts = []
    for idx, item in enumerate(batch, start=1):
        parts.append(f"{idx}. Context:\n{item['context']}\nText: {item['text']}\n")

    # Full prompt with RAG integration
    user_content = (
            f"Translate the following into **Simplified Chinese for the USA**, "
            "preserving the professional tone and context of a fintech company like PayPal. "
            "Ensure all translations adhere to PayPal’s Simplified Chinese for USA glossary where applicable (retrieved via RAG). "
            "If no glossary term is found, use any additional contextual information or relevant metadata available to you, "
            "or rely on other established best practices for accurate translations.\n\n"
            + "".join(parts)
    )

    payload = {
        "prompt_template": {
            "template_messages": [
                {
                    "role": "system",
                    "content": (
                        "Context:\n"
                        "You are a translation assistant specializing in financial and technical content for PayPal. "
                        "Your task is to translate the provided English text string into Simplified Chinese for the USA, "
                        "adhering to PayPal’s tone and style guidelines, "
                        "while ensuring technical and financial terminology is used consistently and accurately. "
                        "You will have access to context from PayPal’s US Simplified Chinese glossary (retrieved via RAG). "
                        "Use this information to inform your translation "
                        "and ensure the highest quality and consistency.\n\n"

                        "Task Instructions:\n"
                        "1. Translate the source string into US Simplified Chinese, maintaining a fluent, "
                        "natural, and precise tone that aligns with PayPal's corporate communication style.\n"
                        "2. Use terms from PayPal’s US Simplified Chinese glossary (retrieved via RAG) wherever applicable to find the most appropriate translation.\n"
                        "2. Do **not** alter or remove any placeholders. Always maintain placeholders exactly. "
                        "You must translate only the static text, preserving each placeholder’s form.\n"
                        "4. When translating, make sure to consider the regional-specific requirements for Simplified Chinese used in the USA. "
                        "Ensure the tone and phrasing reflect the expectations of a US Simplified Chinese speaking audience while keeping the translation culturally sensitive.\n\n"

                        "Style Guidelines:\n"
                        "1. Maintain a formal tone in most scenarios. Exceptions may apply for marketing or call-to-action content, "
                        "where a more informal tone may be appropriate.\n"
                        "2. When addressing users, always use the polite form of 'you' (您) to maintain respect and formality. For example, "
                        "in user instructions, use '请您输入您的日期' (Please enter your date).\n"
                        "3. Avoid literal translations; always focus on the intended meaning of the source text and express it naturally "
                        "in the target language. Use active voice whenever possible to improve readability.\n"
                        "4. Adhere to specific punctuation and formatting rules for Simplified Chinese. Use **double-byte Chinese punctuation** "
                        "where applicable.\n"
                        "5. **Do not use PayPal as a verb** (e.g., 'PayPal it') to protect intellectual property and avoid the casualization of the brand.\n\n"


                        "Quality Review:\n"
                        "1. Proofread your translation thoroughly to ensure linguistic accuracy and clarity.\n"
                        "2. Double-check for consistent **capitalization**, **punctuation**, and **spacing**. Ensure that the translation maintains "
                        "the same formatting as the source text where appropriate.\n"
                        "3. Verify that the translation **faithfully conveys** the meaning of the original string without altering the core message.\n"
                        "4. Pay special attention to **context retrieved from the glossary via RAG**. Ensure that the relevant context is applied correctly.\n\n"

                        "Output:\n"
                        "Provide the translated text as a **JSON array**. Ensure that the JSON format is compatible with PayPal’s system integration, "
                        "with appropriate key-value pairs.\n"
                        "Verify that the translations adhere to the guidelines outlined above and that any additional context retrieved through RAG "
                        "is incorporated properly."
                    )
                },
                {
                    "role": "user",
                    "content": user_content
                }
            ],
            "llm_configuration": {
                "model_name": MODEL_NAME,
                "temperature": 0.1,
                "top_p": 0.3
            },
            "json_schema": {
                "type": "object",
                "properties": {
                    str(i): {"type": "string"} for i in range(1, len(batch) + 1)
                },
                "required": [str(i) for i in range(1, len(batch) + 1)]
            }
        }
    }

    resp = requests.post(API_URL, json=payload, timeout=TIMEOUT)
    resp.raise_for_status()
    data = resp.json()
    if data.get("is_json") and isinstance(data.get("llm_output"), dict):
        data = data["llm_output"]

    out = {}
    for i, item in enumerate(batch, start=1):
        out[(item["row"], item["col"])] = data.get(str(i), "")
    return out


def translate_workbook():
    wb, ws, items = read_workbook(INPUT_XLSX)
    print(f"Found {len(items)} text strings to translate...")

    texts = [text for _, _, text in items]
    contexts = batched_rag_search(texts)

    combined = [
        {"row": r, "col": c, "text": t, "context": ctx}
        for (r, c, t), ctx in zip(items, contexts)
    ]
    batches = [combined[i: i + GROUP_SIZE] for i in range(0, len(combined), GROUP_SIZE)]

    translations = {}
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = {executor.submit(translate_batch, b): b for b in batches}
        for future in as_completed(futures):
            try:
                translations.update(future.result())
            except Exception as e:
                print("Batch error:", e)

    for (r, c), txt in translations.items():
        ws.cell(row=r, column=c, value=txt)

    wb.save(OUTPUT_XLSX)
    print("Complete")


if __name__ == "__main__":
    translate_workbook()