# GLE (GlobalLink Enterprise) Integration

This document describes the integration with TransPerfect's GlobalLink Enterprise system for automated translation quality evaluation.

## Overview

The GLE integration provides a complete workflow for:
1. Authenticating with the GLE system
2. Retrieving claimable submissions for API_QE phase
3. Claiming submissions and languages
4. Downloading TXLF files
5. Performing translation quality evaluation on mt-suggestion segments
6. Updating TXLF files with evaluation results
7. Saving processed files

## Architecture

### Components

1. **GLEService** (`app/gle/service.py`)
   - Handles authentication and API communication with GLE
   - Manages token refresh and expiration
   - Provides methods for claiming, downloading, and processing submissions

2. **TXLFProcessor** (`app/gle/txlf_processor.py`)
   - Parses and processes TXLF files
   - Extracts segments with `state-qualifier="mt-suggestion"`
   - Updates segments with evaluation results
   - Adds QE evaluation status to files

3. **GLEWorkflow** (`app/gle/workflow.py`)
   - Orchestrates the complete end-to-end workflow
   - <PERSON><PERSON> error recovery and logging
   - Provides both complete workflow and single submission processing

4. **API Endpoints** (`app/gle/api.py`)
   - REST API endpoints for triggering workflows
   - Health check and status endpoints
   - File download endpoints

## API Endpoints

### 1. Run Complete Workflow
```
POST /gle/workflow/run
```

Runs the complete GLE workflow to process all claimable submissions.

**Request Body:**
```json
{
  "prompt_template": {
    "template_messages": [...],
    "llm_configuration": {...},
    "output_schema": [...]
  },
  "rag_configs": [...],
  "output_dir": "/tmp/gle_output"
}
```

**Response:**
```json
{
  "success": true,
  "submissions_processed": 3,
  "files_processed": 15,
  "files_passed": 12,
  "output_files": [...],
  "errors": [...]
}
```

### 2. Process Single Submission
```
POST /gle/submission/process
```

Processes a specific submission by ID.

**Request Body:**
```json
{
  "submission_id": 4942,
  "prompt_template": {...},
  "rag_configs": [...],
  "output_dir": "/tmp/gle_submission_4942"
}
```

### 3. Get Claimable Submissions
```
GET /gle/submissions/claimable
```

Returns all claimable submissions for the API_QE phase.

**Response:**
```json
{
  "submissions": [
    {
      "submission_id": 4942,
      "submission_name": "Test_LT-14227_20240403",
      "languages": [...]
    }
  ]
}
```

### 4. Health Check
```
GET /gle/health
```

Checks the health of the GLE integration.

### 5. Download Processed File
```
GET /gle/files/download/{file_path}
```

Downloads a processed TXLF file.

## Configuration

### Environment Variables

The following environment variables can be used to configure the GLE integration:

- `GLE_BASE_URL`: Base URL for the GLE system (default: https://stg-paypal1.transperfect.com)
- `GLE_USERNAME`: Username for authentication (default: paypal_api_qe)
- `GLE_PASSWORD`: Password for authentication
- `GLE_AUTH_HEADER`: Base64 encoded authorization header

### Authentication

The system uses OAuth2 password grant flow for authentication:
- Initial authentication provides access token and refresh token
- Access tokens expire after 20 minutes
- Automatic token refresh using refresh token
- Fallback to re-authentication if refresh fails

## TXLF Processing Rules

### Segment Selection
- Only processes segments with `state-qualifier="mt-suggestion"`
- Ignores segments with other state qualifiers

### Evaluation Results
- Adds `gs4tr:score="{score}"` attribute to target elements
- If score ≥ 95, adds `gs4tr:locked="true"`
- If all segments score ≥ 95, adds `gs4tr:qe-evaluation="pass"` to file element

### Example TXLF Updates

**Before:**
```xml
<target state-qualifier="mt-suggestion">Translated text</target>
```

**After (score ≥ 95):**
```xml
<target state-qualifier="mt-suggestion" gs4tr:score="96" gs4tr:locked="true">Translated text</target>
```

**File-level pass indicator:**
```xml
<file gs4tr:xliff-module-version="6.15.0" original="..." gs4tr:qe-evaluation="pass">
```

## Usage Examples

### 1. Using the API

```python
import requests

# Run complete workflow
response = requests.post("http://localhost:8080/gle/workflow/run", json={
    "prompt_template": {
        "template_messages": [
            {
                "role": "system",
                "content": "You are a translation quality evaluator..."
            },
            {
                "role": "user", 
                "content": "Evaluate: Source: {{source_text}} Target: {{target_text}}"
            }
        ],
        "llm_configuration": {
            "model_name": "google/gemini-2.0-flash",
            "temperature": 0.0,
            "top_p": 0.5
        },
        "output_schema": [
            {
                "name": "score",
                "type": "number",
                "description": "Quality score from 1-100",
                "required": true
            }
        ]
    },
    "rag_configs": [
        {
            "collection_name": "paypal_glossary",
            "limit": 5,
            "placeholder_key": "glossary"
        }
    ]
})

print(response.json())
```

### 2. Using the Workflow Directly

```python
import asyncio
from app.gle.workflow import GLEWorkflow
from app.llm_translation_qe.schemas import PromptTemplate

async def run_workflow():
    workflow = GLEWorkflow()
    
    # Create prompt template
    prompt_template = PromptTemplate(...)
    
    # Run workflow
    results = await workflow.run_complete_workflow(
        prompt_template=prompt_template,
        rag_configs=[],
        output_dir="/tmp/gle_output"
    )
    
    print(f"Processed {results['files_processed']} files")

asyncio.run(run_workflow())
```

### 3. Testing the Integration

```bash
# Run the test script
python test_gle_integration.py

# Test specific functionality
python -c "
import asyncio
from app.gle.service import GLEService

async def test():
    service = GLEService()
    success = await service.authenticate()
    print(f'Authentication: {success}')

asyncio.run(test())
"
```

## Error Handling

The integration includes comprehensive error handling:

1. **Authentication Errors**: Automatic retry with re-authentication
2. **Network Errors**: Retry logic with exponential backoff
3. **File Processing Errors**: Fallback to mock evaluation
4. **Download Timeouts**: Configurable timeout with status checking

## Logging

All operations are logged using the `loguru` logger:
- Authentication events
- Submission processing status
- File processing results
- Error conditions and recovery

## Security Considerations

1. **Credentials**: Store credentials securely using environment variables
2. **File Access**: Restrict file download paths to authorized directories
3. **Token Management**: Automatic token refresh prevents credential exposure
4. **Input Validation**: All API inputs are validated using Pydantic models

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check credentials in environment variables
   - Verify GLE system availability
   - Check network connectivity

2. **No Claimable Submissions**
   - Verify submissions exist in API_QE phase
   - Check user permissions
   - Ensure submissions have LANGUAGE claim level

3. **Download Timeout**
   - Increase timeout values
   - Check GLE system performance
   - Verify submission contains files

4. **TXLF Processing Errors**
   - Validate TXLF file format
   - Check XML namespace declarations
   - Verify segment structure

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Considerations

1. **Concurrent Processing**: Process multiple submissions in parallel
2. **Batch Operations**: Group API calls where possible
3. **Caching**: Cache authentication tokens
4. **Resource Management**: Clean up temporary files

## Future Enhancements

1. **Webhook Support**: Real-time notifications for submission updates
2. **Batch Processing**: Process multiple files simultaneously
3. **Custom Evaluation Rules**: Configurable evaluation criteria
4. **Integration Testing**: Automated testing with mock GLE server
5. **Monitoring**: Metrics and alerting for workflow health
