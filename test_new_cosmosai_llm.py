"""
Test script for the new CosmosAI LLM integration.
"""
import asyncio
import os
from typing import Dict, List

from app.llm.llm_factory import LLMFactory
from app.llm.new_cosmosai_llm import CosmosAILLM


def test_model_info():
    """Test getting model information."""
    print("=" * 50)
    print("COSMOSAI MODEL INFO")
    print("=" * 50)
    
    model_info = CosmosAILLM.get_model_info()
    print(f"Provider: {model_info['provider']}")
    print(f"Base URL: {model_info['base_url']}")
    print(f"API Type: {model_info['api_type']}")
    print(f"Features: {', '.join(model_info['features'])}")
    print(f"Supported Models ({len(model_info['supported_models'])}):")
    for model in model_info['supported_models']:
        print(f"  - {model}")


def test_factory_integration():
    """Test LLM factory integration."""
    print("\n" + "=" * 50)
    print("FACTORY INTEGRATION TEST")
    print("=" * 50)
    
    # Test models to try
    test_models = [
        "gpt-4o-mini",
        "claude-3-5-haiku-20241022",
        "llama31-8b"
    ]
    
    for model_name in test_models:
        try:
            print(f"\nTesting factory creation for: {model_name}")
            llm = LLMFactory.build_llm(model_name)
            print(f"✓ Successfully created LLM instance: {type(llm).__name__}")
            print(f"  Model name: {llm.model_name}")
        except Exception as e:
            print(f"✗ Failed to create LLM for {model_name}: {e}")


async def test_chat_completion():
    """Test chat completion with CosmosAI models."""
    print("\n" + "=" * 50)
    print("CHAT COMPLETION TEST")
    print("=" * 50)
    
    # Set API key for testing (you'll need to replace this)
    api_key = os.environ.get("COSMOSAI_API_KEY", "<LLM_API_KEY>")
    if api_key == "<LLM_API_KEY>":
        print("⚠️  Please set COSMOSAI_API_KEY environment variable for testing")
        return
    
    # Test models (start with smaller/faster ones)
    test_models = [
        "gpt-4o-mini",
        "llama31-8b",
        "claude-3-5-haiku-20241022"
    ]
    
    test_messages = [
        {"role": "user", "content": "Hi, who are you? Please respond in one sentence."}
    ]
    
    llm_config = {
        "temperature": 0.0,
        "max_tokens": 64,
        "top_p": 0.5
    }
    
    for model_name in test_models:
        try:
            print(f"\nTesting chat completion with: {model_name}")
            
            # Create LLM instance
            llm = CosmosAILLM(model_name)
            
            # Test sync chat
            print("  Testing sync chat...")
            response = llm.chat(test_messages, llm_config)
            print(f"  ✓ Sync response: {response[:100]}...")
            
            # Test async chat
            print("  Testing async chat...")
            async_response = await llm.async_chat(test_messages, llm_config)
            print(f"  ✓ Async response: {async_response[:100]}...")
            
        except Exception as e:
            print(f"  ✗ Error testing {model_name}: {e}")


async def test_structured_output():
    """Test structured output functionality."""
    print("\n" + "=" * 50)
    print("STRUCTURED OUTPUT TEST")
    print("=" * 50)
    
    api_key = os.environ.get("COSMOSAI_API_KEY", "<LLM_API_KEY>")
    if api_key == "<LLM_API_KEY>":
        print("⚠️  Please set COSMOSAI_API_KEY environment variable for testing")
        return
    
    # Test with a model that supports structured output
    model_name = "gpt-4o-mini"
    
    messages = [
        {
            "role": "user", 
            "content": "Evaluate this translation quality: Source: 'Hello world' Target: 'Hola mundo'. Provide a score and category."
        }
    ]
    
    # Define structured output schema
    schema = {
        "type": "object",
        "properties": {
            "score": {
                "type": "number",
                "description": "Quality score from 1-100"
            },
            "category": {
                "type": "string", 
                "description": "Error category or 'None'"
            },
            "comment": {
                "type": "string",
                "description": "Brief comment"
            }
        },
        "required": ["score", "category", "comment"]
    }
    
    llm_config = {
        "temperature": 0.0,
        "max_tokens": 128
    }
    
    try:
        print(f"Testing structured output with: {model_name}")
        
        llm = CosmosAILLM(model_name)
        response = llm.chat(messages, llm_config, schema)
        
        print(f"✓ Structured response: {response}")
        print(f"  Type: {type(response)}")
        
        if isinstance(response, dict):
            print("  Parsed fields:")
            for key, value in response.items():
                print(f"    {key}: {value}")
        
    except Exception as e:
        print(f"✗ Error testing structured output: {e}")


def test_available_models():
    """Test getting available models from CosmosAI."""
    print("\n" + "=" * 50)
    print("AVAILABLE MODELS TEST")
    print("=" * 50)
    
    api_key = os.environ.get("COSMOSAI_API_KEY", "<LLM_API_KEY>")
    if api_key == "<LLM_API_KEY>":
        print("⚠️  Please set COSMOSAI_API_KEY environment variable for testing")
        return
    
    try:
        # Create a CosmosAI LLM instance
        llm = CosmosAILLM("gpt-4o-mini")  # Use any model for this test
        
        print("Fetching available models from CosmosAI...")
        available_models = llm.get_available_models()
        
        print(f"✓ Found {len(available_models)} available models:")
        for model in available_models:
            print(f"  - {model}")
            
    except Exception as e:
        print(f"✗ Error getting available models: {e}")


def test_model_testing():
    """Test the model testing functionality."""
    print("\n" + "=" * 50)
    print("MODEL TESTING FUNCTIONALITY")
    print("=" * 50)
    
    api_key = os.environ.get("COSMOSAI_API_KEY", "<LLM_API_KEY>")
    if api_key == "<LLM_API_KEY>":
        print("⚠️  Please set COSMOSAI_API_KEY environment variable for testing")
        return
    
    test_models = ["gpt-4o-mini", "llama31-8b", "claude-3-5-haiku-20241022"]
    
    for model_name in test_models:
        try:
            print(f"\nTesting model health: {model_name}")
            llm = CosmosAILLM(model_name)
            
            is_working = llm.test_model("Hello, respond with 'OK' if you can hear me.")
            status = "✓ Working" if is_working else "✗ Not working"
            print(f"  {status}")
            
        except Exception as e:
            print(f"  ✗ Error testing {model_name}: {e}")


async def main():
    """Run all tests."""
    print("COSMOSAI LLM INTEGRATION TESTS")
    print("=" * 50)
    
    # Basic tests (don't require API key)
    test_model_info()
    test_factory_integration()
    
    # API tests (require API key)
    await test_chat_completion()
    await test_structured_output()
    test_available_models()
    test_model_testing()
    
    print("\n" + "=" * 50)
    print("TESTS COMPLETED")
    print("=" * 50)


if __name__ == "__main__":
    # Set up environment
    print("Setting up test environment...")
    
    # You can set the API key here for testing
    # os.environ["COSMOSAI_API_KEY"] = "your_actual_api_key_here"
    
    # Run tests
    asyncio.run(main())
