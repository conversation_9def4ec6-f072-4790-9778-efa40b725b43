import asyncio
import os
import pandas as pd
from loguru import logger

# Mock evaluator for testing without LLM dependencies
class MockExcelEvaluator:
    async def evaluate_excel_file(self, file_path, prompt_template=None, rag_configs=None, output_dir="/tmp"):
        """Mock implementation that adds evaluation columns without calling LLM"""
        logger.info(f"Mock evaluating Excel file: {file_path}")

        # Read the Excel file
        df = pd.read_excel(file_path)

        # Add evaluation columns if they don't exist with appropriate data types
        if "QE Score" not in df.columns:
            df["QE Score"] = pd.Series(dtype=float)
        if "Error category" not in df.columns:
            df["Error category"] = pd.Series(dtype=str)
        if "Error severity" not in df.columns:
            df["Error severity"] = pd.Series(dtype=str)
        if "Error comment" not in df.columns:
            df["Error comment"] = pd.Series(dtype=str)

        # Fill in mock evaluation data
        for idx, row in df.iterrows():
            # Skip if source or target is empty
            if pd.isna(row["Segment Source"]) or pd.isna(row["Segment Target"]):
                continue

            # Simple mock evaluation logic
            source = str(row["Segment Source"]).lower()
            target = str(row["Segment Target"]).lower()

            # Check for potential errors
            has_error = False
            error_category = ""
            error_severity = ""
            error_comment = ""

            # Simple length check
            if len(source.split()) > len(target.split()) + 3:
                has_error = True
                error_category = "Omission"
                error_severity = "major"
                error_comment = "Target text is significantly shorter than source, suggesting omitted content."
            elif len(target.split()) > len(source.split()) + 3:
                has_error = True
                error_category = "Addition"
                error_severity = "minor"
                error_comment = "Target text is significantly longer than source, suggesting added content."

            # Check for untranslated terms
            english_terms = ["click", "login", "password", "account", "payment"]
            for term in english_terms:
                if term in source and term in target and not "PayPal" in term:
                    has_error = True
                    error_category = "Untranslated Content"
                    error_severity = "minor"
                    error_comment = f"Term '{term}' appears to be untranslated."

            # Set score based on errors
            score = 95 if not has_error else 85

            # Update the dataframe
            df.at[idx, "QE Score"] = score
            df.at[idx, "Error category"] = error_category
            df.at[idx, "Error severity"] = error_severity
            df.at[idx, "Error comment"] = error_comment

            logger.info(f"Evaluated segment {idx+1}: Score={score}, Category={error_category}")

        # Save the updated dataframe
        import time
        timestamp = int(time.time())
        file_name = os.path.basename(file_path)
        base_name, ext = os.path.splitext(file_name)
        output_file_name = f"{base_name}_evaluated_{timestamp}{ext}"
        output_path = os.path.join(output_dir, output_file_name)

        df.to_excel(output_path, index=False)
        logger.info(f"Saved evaluated file to {output_path}")

        return output_path, len(df)

# Try to import the real evaluator, fall back to mock if dependencies are missing
try:
    from app.llm_translation_qe.excel_evaluator import ExcelTranslationEvaluator
    from app.llm_translation_qe.schemas import PromptTemplate, TemplateMessage, OutputSchemaItem, LLMConfiguration
    logger.info("Using real ExcelTranslationEvaluator")
    Evaluator = ExcelTranslationEvaluator
    has_real_evaluator = True
except ImportError as e:
    logger.warning(f"Could not import ExcelTranslationEvaluator: {e}")
    logger.warning("Using MockExcelEvaluator instead")
    Evaluator = MockExcelEvaluator
    has_real_evaluator = False


async def test_excel_evaluation():
    """
    Test the Excel file evaluation functionality.
    """
    # Path to the Excel file
    excel_file_path = "QE Test Excel Example.xlsx"

    # Check if file exists
    if not os.path.exists(excel_file_path):
        logger.error(f"File not found: {excel_file_path}")
        return

    # Create an instance of the evaluator
    evaluator = Evaluator()

    # Create a sample prompt template
    if has_real_evaluator:
        # Create a sample prompt template based on the frontend example
        template_messages = [
            TemplateMessage(
                role="system",
                content="You are a professional translation quality evaluator. Your task is to evaluate the quality of "
                        "translation from source language to target language. Provide a score from 1 to 100, identify "
                        "any error categories, and determine the severity of errors."
            ),
            TemplateMessage(
                role="user",
                content="Please evaluate the following translation:\n\n"
                        "Source text: {{source_text}}\n\n"
                        "Target text: {{target_text}}\n\n"
                        "Provide the following in your evaluation:\n"
                        "1. A quality score between 1 and 100, with 1 being very poor and 100 being excellent. "
                        "If translation is error-free, score should be 95 or above. "
                        "If there are one or more errors, even if minor, score should be 94 or below.\n"
                        "2. An error category (if an error is found - otherwise 'None')\n"
                        "3. An error severity (minor, major or critical - blank if no error)\n"
                        "4. A comment explaining what the error is (blank if no error)"
            )
        ]

        output_schema = [
            OutputSchemaItem(
                name="score",
                type="number",
                description="Translation quality score from 1 to 100",
                required=True
            ),
            OutputSchemaItem(
                name="error_category",
                type="string",
                description="Primary error category found in the translation (or 'None' if no errors)",
                required=True
            ),
            OutputSchemaItem(
                name="error_severity",
                type="string",
                description="Error severity: 'minor', 'major', 'critical', or empty string if no error",
                required=True
            ),
            OutputSchemaItem(
                name="comment",
                type="string",
                description="Brief explanation of the error, or empty string if no error",
                required=True
            )
        ]

        llm_configuration = LLMConfiguration(
            model_name="google/gemini-1.5-flash",
            temperature=0.0,
            top_p=0.5
        )

        prompt_template = PromptTemplate(
            template_messages=template_messages,
            template_variables=[],
            llm_configuration=llm_configuration,
            output_schema=output_schema
        )
    else:
        # For mock evaluator, prompt_template can be None
        prompt_template = None

    # Evaluate the Excel file
    try:
        output_path, segments_evaluated = await evaluator.evaluate_excel_file(
            file_path=excel_file_path,
            prompt_template=prompt_template,
            rag_configs=[],
            output_dir="/tmp"
        )
        logger.info(f"Successfully evaluated Excel file with {segments_evaluated} segments")
        logger.info(f"Output saved to: {output_path}")

        # Print a message to help the user find the file
        print(f"\n\nEvaluation completed successfully!")
        print(f"Output file saved to: {output_path}")
        print(f"You can open this file to see the evaluation results.\n")
    except Exception as e:
        logger.error(f"Error evaluating Excel file: {e}")


if __name__ == "__main__":
    # Configure logger
    logger.add("excel_evaluation.log", rotation="10 MB")
    logger.info("Starting Excel evaluation test")

    try:
        asyncio.run(test_excel_evaluation())
        logger.info("Excel evaluation test completed successfully")
    except Exception as e:
        logger.exception(f"Excel evaluation test failed: {e}")
