#!/usr/bin/env bash

ROOT_DIR="$(cd "$(dirname "$0")" && pwd)"

run_gum() {
    if [[ "$OSTYPE" == darwin* ]]; then
        if [[ "$(uname -m)" == "arm64" ]]; then
            "${ROOT_DIR}"/gum-osx-arm64 "$@"
        else
            "${ROOT_DIR}"/gum-osx-amd64 "$@"
        fi
    elif [[ "$OSTYPE" == linux* ]]; then
        if [[ "$(uname -m)" == "aarch64" ]]; then
            "${ROOT_DIR}"/gum-linux-arm64 "$@"
        else
            "${ROOT_DIR}"/gum-linux-amd64 "$@"
        fi
    else
        echo "Only MacOS and Linux are supported for gum" 1>&2
        exit 1
    fi
}

run_gum "$@"
