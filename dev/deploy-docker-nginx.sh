#!/usr/bin/env bash

set -eu -o pipefail

DEV_DIR="$(cd "$(dirname "$0")" && pwd)"

# Docker related configs
DOCKERHUB_HOST="dockerhub.paypalcorp.com"
DOCKER_IMAGE="aiplatform/jupiter-dev-gcp-vm-proxy"
JFROG_TOKEN="AKCpBrvGBcZa1Bc4BnodqRqeAPEcwc1itSNSgA1XZnKTqdUXxiJXGfWkMpCdDsogQn7csY3ox"

# Prefix of versions
IMAGE_VERSION_BASE="0.0."

# jq and gum are required to run this script
export PATH=$DEV_DIR/jq:$DEV_DIR/gum:$PATH

docker_image_tags() {
    item="$1"
    case "$item" in
    */*) : ;;                  # namespace/repository syntax, leave as is
    *) item="library/$item" ;; # bare repository name (docker official image); must convert to namespace/repository syntax
    esac
    gum log --level info "Fetching exising versions..."
    curl -fsSL -H "Accept: application/json" -H "X-JFrog-Art-Api: ${JFROG_TOKEN}" "https://${DOCKERHUB_HOST}/v2/${item}/tags/list" | jq --raw-output '.tags[]'
}

list_existing_versions() {
    local image_ver_base_regex=$(echo "^${IMAGE_VERSION_BASE}" | sed 's/\./\\./g')
    docker_image_tags "${DOCKER_IMAGE}" | grep -E "${image_ver_base_regex}" || test $? = 1
}

get_version_number() {
    local version="$1"
    echo "${version#"${IMAGE_VERSION_BASE}"}"
}

# Get the next version number by increasing the max existing version (tag) in DockerHub and $IMAGE_VERSION_BASE
get_next_version() {
    local existing_versions=$(list_existing_versions)

    if [[ -z "${existing_versions}" ]]; then
        echo "${IMAGE_VERSION_BASE}1"
        gum log --level warn "No existing versions found."
        return
    fi

    local max_version_number=0
    for version in ${existing_versions}; do
        local version_number=$(get_version_number "${version}")
        if ((version_number > max_version_number)); then
            max_version_number=${version_number}
        fi
    done
    gum log --level info "Max existing version number is \`${max_version_number}\`"
    local next_version_number=$((max_version_number + 1))
    echo "${IMAGE_VERSION_BASE}${next_version_number}"
}

get_full_docker_image() {
    local version="$1"
    shift
    echo "${DOCKERHUB_HOST}/${DOCKER_IMAGE}:${version}"
}

docker_build() {
    local version="$1"
    shift

    pushd "${DEV_DIR}/.." >/dev/null
    local full_docker_image_name="$(get_full_docker_image "${version}")"
    DOCKER_BUILDKIT=1 docker buildx build --no-cache --platform linux/amd64 -f ./nginx/Dockerfile -t "${full_docker_image_name}" ./nginx
    popd >/dev/null
}

main() {
    echo "Login to dockerhub.paypalcorp.com:"
    docker login dockerhub.paypalcorp.com

    # decide on next version (tag) of image to build and publish
    local next_version="$(get_next_version)"
    gum log --level info "Version prefix is \`${IMAGE_VERSION_BASE}\`"
    gum log --level info "Next version to be published is \`${next_version}\`"

    gum log --level info "Building docker image for version \`${next_version}\`..."
    docker_build "${next_version}"
    gum log --level info "Docker image built successfully"

    local full_docker_image_name="$(get_full_docker_image "${next_version}")"
    gum log --level info "Pushing docker image \`${full_docker_image_name}\`..."
    docker push "${full_docker_image_name}"
    gum log --level info "Docker image ${full_docker_image_name} published successfully"

    echo ":rocket: Done." | gum format -t emoji
}

main "$@"
