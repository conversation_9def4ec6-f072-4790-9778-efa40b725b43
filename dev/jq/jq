#!/usr/bin/env bash

ROOT_DIR="$(cd "$(dirname "$0")" && pwd)"

run_jq() {
    if [[ "$OSTYPE" == darwin* ]]; then
        if [[ "$(uname -m)" == "arm64" ]]; then
            "${ROOT_DIR}"/jq-osx-arm64 "$@"
        else
            "${ROOT_DIR}"/jq-osx-amd64 "$@"
        fi
    elif [[ "$OSTYPE" == linux* ]]; then
        if [[ "$(uname -m)" == "aarch64" ]]; then
            "${ROOT_DIR}"/jq-linux-arm64 "$@"
        else
            "${ROOT_DIR}"/jq-linux-amd64 "$@"
        fi
    else
        echo "Only MacOS and Linux are supported for jq" 1>&2
        exit 1
    fi
}

run_jq "$@"
