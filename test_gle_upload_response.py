"""
Test script to verify GLE upload response validation.
"""
import asyncio
import json
import tempfile
from unittest.mock import AsyncMock, patch
from app.gle.service import GLEService


def create_test_txlf_file():
    """Create a test TXLF file for upload testing."""
    txlf_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:gs4tr="http://www.gs4tr.org/schema/xliff-extensions">
    <file original="test.json" source-language="en" target-language="de" datatype="json">
        <body>
            <trans-unit id="1" gs4tr:segmentId="1" gs4tr:locked="true">
                <source>Hello world</source>
                <target state-qualifier="mt-suggestion" gs4tr:score="96">Hallo Welt</target>
            </trans-unit>
        </body>
    </file>
</xliff>'''
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txlf', delete=False) as f:
        f.write(txlf_content)
        return f.name


async def test_upload_response_validation():
    """Test upload response validation with different response scenarios."""
    print("=" * 60)
    print("GLE UPLOAD RESPONSE VALIDATION TEST")
    print("=" * 60)
    
    # Create test file
    test_file = create_test_txlf_file()
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Successful Upload",
            "status": 200,
            "response": {"submissionId": 5159, "processId": "b27a2c33-2722-40b6-ac74-d0e439e4baed"},
            "expected_result": True
        },
        {
            "name": "Missing processId",
            "status": 200,
            "response": {"submissionId": 5159},
            "expected_result": False
        },
        {
            "name": "Missing submissionId",
            "status": 200,
            "response": {"processId": "b27a2c33-2722-40b6-ac74-d0e439e4baed"},
            "expected_result": False
        },
        {
            "name": "Empty Response",
            "status": 200,
            "response": {},
            "expected_result": False
        },
        {
            "name": "Invalid JSON",
            "status": 200,
            "response": "Invalid JSON response",
            "expected_result": False
        },
        {
            "name": "HTTP Error 400",
            "status": 400,
            "response": {"error": "Bad Request", "message": "Invalid file format"},
            "expected_result": False
        },
        {
            "name": "HTTP Error 401",
            "status": 401,
            "response": {"error": "Unauthorized", "message": "Invalid token"},
            "expected_result": False
        },
        {
            "name": "HTTP Error 500",
            "status": 500,
            "response": {"error": "Internal Server Error"},
            "expected_result": False
        }
    ]
    
    gle_service = GLEService()
    gle_service.access_token = "test_token"  # Mock token
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. Testing: {scenario['name']}")
        print("-" * 40)
        
        try:
            # Mock the HTTP response
            with patch('aiohttp.ClientSession') as mock_session:
                # Create mock response
                mock_response = AsyncMock()
                mock_response.status = scenario['status']
                
                if isinstance(scenario['response'], dict):
                    mock_response.text.return_value = json.dumps(scenario['response'])
                else:
                    mock_response.text.return_value = scenario['response']
                
                mock_response.headers = {'content-type': 'application/json'}
                
                # Mock session context manager
                mock_session_instance = AsyncMock()
                mock_session.return_value.__aenter__.return_value = mock_session_instance
                mock_session_instance.post.return_value.__aenter__.return_value = mock_response
                
                # Test the upload
                result = await gle_service.upload_txlf_file(
                    submission_id=5159,
                    file_path=test_file,
                    original_filename="test.txlf"
                )
                
                # Verify result
                if result == scenario['expected_result']:
                    print(f"   ✓ Result: {result} (as expected)")
                else:
                    print(f"   ✗ Result: {result} (expected: {scenario['expected_result']})")
                
                # Show what would be logged
                print(f"   Response status: {scenario['status']}")
                if isinstance(scenario['response'], dict):
                    print(f"   Response body: {json.dumps(scenario['response'], indent=2)}")
                else:
                    print(f"   Response body: {scenario['response']}")
                
        except Exception as e:
            print(f"   ✗ Test failed with exception: {e}")
    
    # Clean up
    import os
    try:
        os.unlink(test_file)
    except:
        pass


def test_response_parsing_logic():
    """Test the response parsing logic separately."""
    print("\n" + "=" * 60)
    print("RESPONSE PARSING LOGIC TEST")
    print("=" * 60)
    
    test_responses = [
        {
            "name": "Valid Success Response",
            "json_str": '{"submissionId":5159,"processId":"b27a2c33-2722-40b6-ac74-d0e439e4baed"}',
            "expected_valid": True
        },
        {
            "name": "Missing processId",
            "json_str": '{"submissionId":5159}',
            "expected_valid": False
        },
        {
            "name": "Extra Fields (Still Valid)",
            "json_str": '{"submissionId":5159,"processId":"abc-123","timestamp":"2024-01-01","status":"success"}',
            "expected_valid": True
        },
        {
            "name": "Wrong Data Types",
            "json_str": '{"submissionId":"5159","processId":123}',
            "expected_valid": True  # We don't validate types, just presence
        },
        {
            "name": "Null Values",
            "json_str": '{"submissionId":null,"processId":null}',
            "expected_valid": False  # Null values should be treated as missing
        },
        {
            "name": "Empty Strings",
            "json_str": '{"submissionId":"","processId":""}',
            "expected_valid": True  # Empty strings are still present
        }
    ]
    
    for i, test_case in enumerate(test_responses, 1):
        print(f"\n{i}. {test_case['name']}:")
        
        try:
            response_json = json.loads(test_case['json_str'])
            
            # Apply the same logic as in the upload method
            has_submission_id = "submissionId" in response_json and response_json["submissionId"] is not None
            has_process_id = "processId" in response_json and response_json["processId"] is not None
            is_valid = has_submission_id and has_process_id
            
            print(f"   JSON: {test_case['json_str']}")
            print(f"   Has submissionId: {has_submission_id}")
            print(f"   Has processId: {has_process_id}")
            print(f"   Is valid: {is_valid}")
            
            if is_valid == test_case['expected_valid']:
                print(f"   ✓ Result matches expected: {test_case['expected_valid']}")
            else:
                print(f"   ✗ Result {is_valid} doesn't match expected: {test_case['expected_valid']}")
                
        except json.JSONDecodeError as e:
            print(f"   ✗ JSON parsing failed: {e}")
            print(f"   Expected valid: {test_case['expected_valid']}")


def show_curl_command_example():
    """Show the curl command example for reference."""
    print("\n" + "=" * 60)
    print("CURL COMMAND REFERENCE")
    print("=" * 60)
    
    print("Example successful upload command:")
    print("""
curl -X POST "https://stg-paypal1.transperfect.com/PD/rest/v0/submissions/5159/upload/translatable" \\
     -H "Authorization: Bearer <token>" \\
     -H "Content-Type: multipart/form-data" \\
     -F "file=@/path/to/file.txlf" \\
     -F "extractArchive=false"
""")
    
    print("Expected successful response:")
    print("""
{
  "submissionId": 5159,
  "processId": "b27a2c33-2722-40b6-ac74-d0e439e4baed"
}
""")
    
    print("Key validation points:")
    print("✓ HTTP status must be 200")
    print("✓ Response must be valid JSON")
    print("✓ Response must contain 'submissionId' field")
    print("✓ Response must contain 'processId' field")
    print("✓ Both fields must not be null")


async def main():
    """Run all tests."""
    print("GLE UPLOAD RESPONSE VALIDATION TESTS")
    print("=" * 60)
    
    await test_upload_response_validation()
    test_response_parsing_logic()
    show_curl_command_example()
    
    print("\n" + "=" * 60)
    print("TESTS COMPLETED")
    print("=" * 60)
    print("Key improvements:")
    print("✓ Validates response contains both submissionId and processId")
    print("✓ Detailed logging for upload failures")
    print("✓ Proper error handling for different HTTP status codes")
    print("✓ JSON parsing error handling")
    print("✓ Enhanced debugging information")


if __name__ == "__main__":
    asyncio.run(main())
