"""
Test script to verify TXLF structure preservation.
"""
import asyncio
import os
import tempfile
import xml.etree.ElementTree as ET
from app.gle.txlf_processor import TXLFProcessor
from app.llm_translation_qe.schemas import (
    PromptTemplate, 
    TemplateMessage, 
    LLMConfiguration, 
    OutputSchemaItem
)


def create_test_txlf_files():
    """Create test TXLF files with different structures."""
    
    # Test file 1: Standard structure with gs4tr namespace
    txlf1_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:gs4tr="http://www.gs4tr.org/schema/xliff-extensions">
    <file original="test.json" source-language="en" target-language="de" datatype="json">
        <body>
            <trans-unit id="1" gs4tr:segmentId="1">
                <source>Hello world</source>
                <target state-qualifier="mt-suggestion">Hallo Welt</target>
            </trans-unit>
            <trans-unit id="2" gs4tr:segmentId="2">
                <source>Click here</source>
                <target state-qualifier="mt-suggestion">Hier klicken</target>
            </trans-unit>
        </body>
    </file>
</xliff>'''
    
    # Test file 2: Different namespace prefix
    txlf2_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:gs="http://www.gs4tr.org/schema/xliff-extensions">
    <file original="test2.json" source-language="en" target-language="zh" datatype="json">
        <body>
            <trans-unit id="1" gs:segmentId="1">
                <source>PayPal account</source>
                <target state-qualifier="mt-suggestion">PayPal账户</target>
            </trans-unit>
        </body>
    </file>
</xliff>'''
    
    # Test file 3: No namespace prefix (full URI)
    txlf3_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:g4tr="http://www.gs4tr.org/schema/xliff-extensions">
    <file original="test3.properties" source-language="en" target-language="es" datatype="properties">
        <body>
            <trans-unit id="welcome_msg">
                <source>Welcome to our service</source>
                <target state-qualifier="mt-suggestion">Bienvenido a nuestro servicio</target>
            </trans-unit>
        </body>
    </file>
</xliff>'''
    
    return [
        ("test1.txlf", txlf1_content),
        ("test2.txlf", txlf2_content), 
        ("test3.txlf", txlf3_content)
    ]


def compare_xml_structure(original_content: str, processed_content: str) -> dict:
    """Compare original and processed XML structures."""
    
    result = {
        "xml_declaration_preserved": False,
        "namespaces_preserved": False,
        "structure_preserved": False,
        "attributes_added": [],
        "issues": []
    }
    
    try:
        # Check XML declaration
        orig_lines = original_content.split('\n')
        proc_lines = processed_content.split('\n')
        
        orig_declaration = ""
        proc_declaration = ""
        
        for line in orig_lines:
            if line.strip().startswith('<?xml'):
                orig_declaration = line.strip()
                break
        
        for line in proc_lines:
            if line.strip().startswith('<?xml'):
                proc_declaration = line.strip()
                break
        
        if orig_declaration == proc_declaration:
            result["xml_declaration_preserved"] = True
        else:
            result["issues"].append(f"XML declaration changed: '{orig_declaration}' -> '{proc_declaration}'")
        
        # Parse both XMLs
        orig_root = ET.fromstring(original_content)
        proc_root = ET.fromstring(processed_content)
        
        # Check namespace preservation
        orig_namespaces = set()
        proc_namespaces = set()
        
        for attr_name, attr_value in orig_root.attrib.items():
            if attr_name.startswith('xmlns'):
                orig_namespaces.add(f"{attr_name}={attr_value}")
        
        for attr_name, attr_value in proc_root.attrib.items():
            if attr_name.startswith('xmlns'):
                proc_namespaces.add(f"{attr_name}={attr_value}")
        
        if orig_namespaces == proc_namespaces:
            result["namespaces_preserved"] = True
        else:
            result["issues"].append(f"Namespaces changed: {orig_namespaces} -> {proc_namespaces}")
        
        # Check for added attributes
        def find_added_attributes(orig_elem, proc_elem, path=""):
            added_attrs = []
            
            # Check current element
            for attr_name, attr_value in proc_elem.attrib.items():
                if attr_name not in orig_elem.attrib:
                    added_attrs.append(f"{path}@{attr_name}={attr_value}")
            
            # Check children
            orig_children = {child.tag: child for child in orig_elem}
            proc_children = {child.tag: child for child in proc_elem}
            
            for tag, proc_child in proc_children.items():
                if tag in orig_children:
                    child_path = f"{path}/{tag}" if path else tag
                    added_attrs.extend(find_added_attributes(orig_children[tag], proc_child, child_path))
            
            return added_attrs
        
        result["attributes_added"] = find_added_attributes(orig_root, proc_root)
        
        # Overall structure check
        if result["xml_declaration_preserved"] and result["namespaces_preserved"]:
            result["structure_preserved"] = True
        
    except Exception as e:
        result["issues"].append(f"Error comparing structures: {e}")
    
    return result


async def test_txlf_structure_preservation():
    """Test TXLF structure preservation."""
    print("=" * 60)
    print("TXLF STRUCTURE PRESERVATION TEST")
    print("=" * 60)
    
    # Create test files
    test_files = create_test_txlf_files()
    
    # Create simple prompt template
    template_messages = [
        TemplateMessage(
            role="system",
            content="You are a translation quality evaluator."
        ),
        TemplateMessage(
            role="user",
            content="Evaluate: Source: {{source_text}}, Target: {{target_text}}"
        )
    ]
    
    llm_config = LLMConfiguration(
        model_name="google/gemini-2.0-flash",
        temperature=0.0
    )
    
    output_schema = [
        OutputSchemaItem(
            name="score",
            type="number",
            description="Quality score",
            required=True
        ),
        OutputSchemaItem(
            name="error_category",
            type="string",
            description="Error category",
            required=True
        ),
        OutputSchemaItem(
            name="error_severity",
            type="string",
            description="Error severity",
            required=True
        ),
        OutputSchemaItem(
            name="comment",
            type="string",
            description="Comment",
            required=True
        )
    ]
    
    prompt_template = PromptTemplate(
        template_messages=template_messages,
        llm_configuration=llm_config,
        output_schema=output_schema
    )
    
    processor = TXLFProcessor()
    
    for i, (filename, content) in enumerate(test_files):
        print(f"\n{i+1}. Testing {filename}")
        print("-" * 40)
        
        # Create temporary files
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txlf', delete=False) as input_file:
            input_file.write(content)
            input_path = input_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txlf', delete=False) as output_file:
            output_path = output_file.name
        
        try:
            # Process the file
            print(f"   Processing {filename}...")
            result = await processor.process_txlf_file(
                input_path, 
                output_path, 
                prompt_template, 
                []  # No RAG configs for this test
            )
            
            if result["success"]:
                print(f"   ✓ Processing successful")
                print(f"   ✓ Segments processed: {result['segments_processed']}")
                
                # Read processed content
                with open(output_path, 'r', encoding='utf-8') as f:
                    processed_content = f.read()
                
                # Compare structures
                comparison = compare_xml_structure(content, processed_content)
                
                print(f"   XML Declaration: {'✓' if comparison['xml_declaration_preserved'] else '✗'}")
                print(f"   Namespaces: {'✓' if comparison['namespaces_preserved'] else '✗'}")
                print(f"   Structure: {'✓' if comparison['structure_preserved'] else '✗'}")
                
                if comparison["attributes_added"]:
                    print(f"   ✓ Attributes added: {len(comparison['attributes_added'])}")
                    for attr in comparison["attributes_added"]:
                        print(f"     - {attr}")
                
                if comparison["issues"]:
                    print(f"   ⚠ Issues found:")
                    for issue in comparison["issues"]:
                        print(f"     - {issue}")
                
                # Show sample of processed content
                print(f"   Sample processed content:")
                lines = processed_content.split('\n')
                for line in lines[:5]:
                    if line.strip():
                        print(f"     {line}")
                if len(lines) > 5:
                    print(f"     ... ({len(lines)} total lines)")
                
            else:
                print(f"   ✗ Processing failed: {result.get('error', 'Unknown error')}")
        
        except Exception as e:
            print(f"   ✗ Test failed: {e}")
        
        finally:
            # Clean up
            try:
                os.unlink(input_path)
                os.unlink(output_path)
            except:
                pass


def test_namespace_detection():
    """Test namespace prefix detection."""
    print("\n" + "=" * 60)
    print("NAMESPACE PREFIX DETECTION TEST")
    print("=" * 60)
    
    test_cases = [
        ('gs4tr namespace', '<target xmlns:gs4tr="http://www.gs4tr.org/schema/xliff-extensions" gs4tr:score="95"/>'),
        ('gs namespace', '<target xmlns:gs="http://www.gs4tr.org/schema/xliff-extensions" gs:score="95"/>'),
        ('g4tr namespace', '<target xmlns:g4tr="http://www.gs4tr.org/schema/xliff-extensions" g4tr:score="95"/>'),
        ('no namespace', '<target score="95"/>'),
    ]
    
    processor = TXLFProcessor()
    
    for name, xml_str in test_cases:
        try:
            print(f"\n{name}:")
            print(f"  XML: {xml_str}")
            
            # Parse the XML
            element = ET.fromstring(xml_str)
            
            # Detect prefix
            prefix = processor._detect_gs4tr_prefix(element)
            print(f"  Detected prefix: {prefix}")
            
        except Exception as e:
            print(f"  Error: {e}")


async def main():
    """Run all tests."""
    print("TXLF STRUCTURE PRESERVATION TESTS")
    print("=" * 60)
    
    await test_txlf_structure_preservation()
    test_namespace_detection()
    
    print("\n" + "=" * 60)
    print("TESTS COMPLETED")
    print("=" * 60)
    print("Key improvements:")
    print("✓ Original XML declaration preserved")
    print("✓ Namespace declarations preserved")
    print("✓ Namespace prefixes detected and used")
    print("✓ XML structure maintained")
    print("✓ Only evaluation attributes added")


if __name__ == "__main__":
    asyncio.run(main())
