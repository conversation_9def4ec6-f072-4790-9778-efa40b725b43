"""
Test script to verify filename encoding/decoding for GLE uploads.
"""
import asyncio
import tempfile
import urllib.parse
from unittest.mock import AsyncMock, patch
from app.gle.service import GLEService


def test_filename_encoding_scenarios():
    """Test different filename encoding scenarios."""
    print("=" * 60)
    print("FILENAME ENCODING TEST SCENARIOS")
    print("=" * 60)
    
    test_filenames = [
        {
            "name": "Normal filename",
            "original": "test_file.txlf",
            "expected": "test_file.txlf"
        },
        {
            "name": "Filename with hash",
            "original": "Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf",
            "expected": "Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf"
        },
        {
            "name": "URL-encoded filename",
            "original": "Test_1-PayPal_JSON-cs-CZ%23APIQE_ETAWT%23.json.txlf",
            "expected": "Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf"
        },
        {
            "name": "Filename with spaces",
            "original": "Test File With Spaces.txlf",
            "expected": "Test File With Spaces.txlf"
        },
        {
            "name": "URL-encoded spaces",
            "original": "Test%20File%20With%20Spaces.txlf",
            "expected": "Test File With Spaces.txlf"
        },
        {
            "name": "Complex filename",
            "original": "DE_en_personalized_guidance-PayPal_Properties-zh%23APIQE_EQRRV%23.properties.txlf",
            "expected": "DE_en_personalized_guidance-PayPal_Properties-zh#APIQE_EQRRV#.properties.txlf"
        },
        {
            "name": "Filename with plus signs",
            "original": "test%2Bfile%2Bwith%2Bplus.txlf",
            "expected": "test+file+with+plus.txlf"
        },
        {
            "name": "Filename with ampersand",
            "original": "test%26file%26with%26ampersand.txlf",
            "expected": "test&file&with&ampersand.txlf"
        }
    ]
    
    for i, test_case in enumerate(test_filenames, 1):
        print(f"\n{i}. {test_case['name']}:")
        print(f"   Original: {test_case['original']}")
        
        # Apply the same logic as in the upload method
        decoded_filename = urllib.parse.unquote(test_case['original'])
        if decoded_filename != test_case['original']:
            upload_filename = decoded_filename
            print(f"   Decoded:  {decoded_filename}")
        else:
            upload_filename = test_case['original']
            print(f"   No change needed")
        
        print(f"   Expected: {test_case['expected']}")
        print(f"   Result:   {upload_filename}")
        
        if upload_filename == test_case['expected']:
            print(f"   ✓ PASS")
        else:
            print(f"   ✗ FAIL")


def test_special_characters_detection():
    """Test detection of special characters in filenames."""
    print("\n" + "=" * 60)
    print("SPECIAL CHARACTERS DETECTION TEST")
    print("=" * 60)
    
    test_filenames = [
        "normal_file.txlf",
        "file_with_#_hash.txlf",
        "file_with_%_percent.txlf",
        "file_with_&_ampersand.txlf",
        "file_with_+_plus.txlf",
        "file_with_=_equals.txlf",
        "file_with_?_question.txlf",
        "Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf"
    ]
    
    special_chars = ['#', '%', '&', '+', '=', '?']
    
    for filename in test_filenames:
        print(f"\nFilename: {filename}")
        
        found_chars = [char for char in special_chars if char in filename]
        
        if found_chars:
            print(f"  Special characters found: {found_chars}")
        else:
            print(f"  No special characters found")


async def test_upload_with_special_filename():
    """Test upload functionality with special characters in filename."""
    print("\n" + "=" * 60)
    print("UPLOAD TEST WITH SPECIAL FILENAME")
    print("=" * 60)
    
    # Create test TXLF file
    txlf_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2" xmlns:gs4tr="http://www.gs4tr.org/schema/xliff-extensions">
    <file original="test.json" source-language="en" target-language="cs-CZ" datatype="json">
        <body>
            <trans-unit id="1" gs4tr:segmentId="1" gs4tr:locked="true">
                <source>Hello world</source>
                <target state-qualifier="mt-suggestion" gs4tr:score="96">Ahoj světe</target>
            </trans-unit>
        </body>
    </file>
</xliff>'''
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txlf', delete=False) as f:
        f.write(txlf_content)
        test_file_path = f.name
    
    # Test with problematic filename
    problematic_filename = "Test_1-PayPal_JSON-cs-CZ%23APIQE_ETAWT%23.json.txlf"
    expected_filename = "Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf"
    
    print(f"Testing upload with filename: {problematic_filename}")
    print(f"Expected to be corrected to: {expected_filename}")
    
    try:
        gle_service = GLEService()
        gle_service.access_token = "test_token"
        
        # Mock successful response
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text.return_value = '{"submissionId":5159,"processId":"test-process-id"}'
            mock_response.headers = {'content-type': 'application/json'}
            
            mock_session_instance = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_session_instance
            mock_session_instance.post.return_value.__aenter__.return_value = mock_response
            
            # Test upload
            result = await gle_service.upload_txlf_file(
                submission_id=5159,
                file_path=test_file_path,
                original_filename=problematic_filename
            )
            
            print(f"Upload result: {result}")
            
            # Check what filename was actually used in the request
            # This would be visible in the logs
            print("Check the logs above to see the filename processing")
    
    except Exception as e:
        print(f"Test failed: {e}")
    
    finally:
        # Clean up
        import os
        try:
            os.unlink(test_file_path)
        except:
            pass


def show_curl_comparison():
    """Show curl command comparison."""
    print("\n" + "=" * 60)
    print("CURL COMMAND COMPARISON")
    print("=" * 60)
    
    print("Original curl command (working):")
    print("""
curl -X POST "https://stg-paypal1.transperfect.com/PD/rest/v0/submissions/5159/upload/translatable" \\
     -H "Authorization: Bearer <token>" \\
     -H "Content-Type: multipart/form-data" \\
     -F "file=@/tmp/Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf" \\
     -F "extractArchive=false"
""")
    
    print("Problematic filename (URL-encoded):")
    print("Test_1-PayPal_JSON-cs-CZ%23APIQE_ETAWT%23.json.txlf")
    
    print("Correct filename (with # characters):")
    print("Test_1-PayPal_JSON-cs-CZ#APIQE_ETAWT#.json.txlf")
    
    print("\nKey points:")
    print("✓ GLE expects the original filename with # characters")
    print("✓ URL encoding (%23) should be decoded back to #")
    print("✓ The filename in the multipart form should not be URL-encoded")


async def main():
    """Run all tests."""
    print("FILENAME ENCODING TESTS FOR GLE UPLOAD")
    print("=" * 60)
    
    test_filename_encoding_scenarios()
    test_special_characters_detection()
    await test_upload_with_special_filename()
    show_curl_comparison()
    
    print("\n" + "=" * 60)
    print("TESTS COMPLETED")
    print("=" * 60)
    print("Key fixes implemented:")
    print("✓ URL-decode filenames before upload")
    print("✓ Detect and log special characters in filenames")
    print("✓ Preserve original # characters in filenames")
    print("✓ Enhanced logging for filename processing")


if __name__ == "__main__":
    asyncio.run(main())
