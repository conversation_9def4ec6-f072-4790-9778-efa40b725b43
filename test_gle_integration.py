"""
Test script for GLE integration.
"""
import asyncio
import j<PERSON>
from datetime import datetime

from app.gle.workflow import GLEWorkflow
from app.llm_translation_qe.schemas import (
    PromptTemplate, 
    TemplateMessage, 
    LLMConfiguration, 
    OutputSchemaItem,
    RAGConfig
)


async def test_gle_workflow():
    """Test the complete GLE workflow."""
    
    # Create a sample prompt template for translation quality evaluation
    template_messages = [
        TemplateMessage(
            role="system",
            content="You are a professional translation quality evaluator. Your task is to evaluate the quality of "
                   "translation from source language to target language. Provide a score from 1 to 100, identify "
                   "any error categories, and determine the severity of errors."
        ),
        TemplateMessage(
            role="user",
            content="Please evaluate the following translation:\n\n"
                   "Source text: {{source_text}}\n\n"
                   "Target text: {{target_text}}\n\n"
                   "Provide the following in your evaluation:\n"
                   "1. A quality score between 1 and 100, with 1 being very poor and 100 being excellent. "
                   "If translation is error-free, score should be 95 or above. "
                   "If there are one or more errors, even if minor, score should be 94 or below.\n"
                   "2. An error category (if an error is found - otherwise 'None')\n"
                   "3. An error severity (minor, major or critical - blank if no error)\n"
                   "4. A comment explaining what the error is (blank if no error)\n\n"
                   "Please refer to the retrieval document: {{glossary}}"
        )
    ]
    
    output_schema = [
        OutputSchemaItem(
            name="score",
            type="number",
            description="Translation quality score from 1 to 100",
            required=True
        ),
        OutputSchemaItem(
            name="error_category",
            type="string",
            description="Primary error category found in the translation (or 'None' if no errors)",
            required=True
        ),
        OutputSchemaItem(
            name="error_severity",
            type="string",
            description="Error severity: 'minor', 'major', 'critical', or empty string if no error",
            required=True
        ),
        OutputSchemaItem(
            name="comment",
            type="string",
            description="Brief explanation of the error, or empty string if no error",
            required=True
        )
    ]
    
    llm_configuration = LLMConfiguration(
        model_name="google/gemini-2.0-flash",
        temperature=0.0,
        top_p=0.5
    )
    
    prompt_template = PromptTemplate(
        template_messages=template_messages,
        template_variables=[],
        llm_configuration=llm_configuration,
        output_schema=output_schema
    )
    
    # Create RAG configs (optional)
    rag_configs = [
        RAGConfig(
            collection_name="paypal_glossary",
            texts=[],  # Will be populated automatically
            limit=5,
            placeholder_key="glossary"
        )
    ]
    
    # Create workflow instance
    workflow = GLEWorkflow()
    
    print("Starting GLE workflow test...")
    print(f"Timestamp: {datetime.now()}")
    
    try:
        # Run the complete workflow
        results = await workflow.run_complete_workflow(
            prompt_template=prompt_template,
            rag_configs=rag_configs,
            output_dir="/tmp/gle_test_output"
        )
        
        print("\n" + "="*50)
        print("GLE WORKFLOW RESULTS")
        print("="*50)
        print(f"Success: {results['success']}")
        print(f"Submissions processed: {results['submissions_processed']}")
        print(f"Files processed: {results['files_processed']}")
        print(f"Files passed: {results['files_passed']}")
        print(f"Output files: {len(results['output_files'])}")
        
        if results['output_files']:
            print("\nOutput files:")
            for file_path in results['output_files']:
                print(f"  - {file_path}")
        
        if results['errors']:
            print(f"\nErrors ({len(results['errors'])}):")
            for error in results['errors']:
                print(f"  - {error}")
        
        print("="*50)
        
        return results
        
    except Exception as e:
        print(f"Error running GLE workflow: {e}")
        return None


async def test_single_submission():
    """Test processing a single submission."""
    
    # Use the same prompt template as above
    template_messages = [
        TemplateMessage(
            role="system",
            content="You are a professional translation quality evaluator."
        ),
        TemplateMessage(
            role="user",
            content="Evaluate this translation:\nSource: {{source_text}}\nTarget: {{target_text}}"
        )
    ]
    
    output_schema = [
        OutputSchemaItem(name="score", type="number", description="Quality score", required=True),
        OutputSchemaItem(name="error_category", type="string", description="Error category", required=True),
        OutputSchemaItem(name="error_severity", type="string", description="Error severity", required=True),
        OutputSchemaItem(name="comment", type="string", description="Comment", required=True)
    ]
    
    llm_configuration = LLMConfiguration(
        model_name="google/gemini-2.0-flash",
        temperature=0.0,
        top_p=0.5
    )
    
    prompt_template = PromptTemplate(
        template_messages=template_messages,
        template_variables=[],
        llm_configuration=llm_configuration,
        output_schema=output_schema
    )
    
    workflow = GLEWorkflow()
    
    # Test with a specific submission ID (you'll need to replace this with an actual ID)
    submission_id = 4942  # Example from the documentation
    
    print(f"\nTesting single submission processing for ID: {submission_id}")
    
    try:
        results = await workflow.process_single_submission(
            submission_id=submission_id,
            prompt_template=prompt_template,
            rag_configs=[],
            output_dir=f"/tmp/gle_submission_{submission_id}"
        )
        
        print(f"Single submission results: {results}")
        return results
        
    except Exception as e:
        print(f"Error processing single submission: {e}")
        return None


async def test_gle_service():
    """Test basic GLE service functionality."""
    from app.gle.service import GLEService
    
    print("Testing GLE service...")
    
    gle_service = GLEService()
    
    try:
        # Test authentication
        print("Testing authentication...")
        auth_success = await gle_service.authenticate()
        print(f"Authentication: {'Success' if auth_success else 'Failed'}")
        
        if auth_success:
            # Test getting claimable submissions
            print("Getting claimable submissions...")
            submissions = await gle_service.get_claimable_submissions()
            print(f"Found {len(submissions)} claimable submissions")
            
            for sub in submissions:
                print(f"  - Submission {sub['submissionId']}: {sub['submissionName']}")
                for lang in sub.get('languages', []):
                    print(f"    Language: {lang['sourceLanguageCode']} -> {lang['targetLanguageCode']}")
        
        return auth_success
        
    except Exception as e:
        print(f"Error testing GLE service: {e}")
        return False


if __name__ == "__main__":
    print("GLE Integration Test")
    print("=" * 50)
    
    # Test basic service functionality
    # asyncio.run(test_gle_service())
    
    print("\n" + "=" * 50)
    
    # Test complete workflow (uncomment to run)
    asyncio.run(test_gle_workflow())
    
    # Test single submission (uncomment to run)
    # asyncio.run(test_single_submission())
    
    print("Test completed.")
