"""
TXLF file processor for translation quality evaluation.
"""
import os
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple

from loguru import logger

from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig, TemplateVariable


class TXLFProcessor:
    """Processor for TXLF files to perform translation quality evaluation."""
    
    def __init__(self):
        self.llm_evaluator = LLMTranslationEvaluator()
        # Define XML namespaces
        self.namespaces = {
            'xliff': 'urn:oasis:names:tc:xliff:document:1.2',
            'gs4tr': 'http://www.gs4tr.org/schema/xliff-ext'
        }
    
    def parse_txlf_file(self, file_path: str) -> Optional[ET.ElementTree]:
        """
        Parse a TXLF file and return the XML tree.
        
        Args:
            file_path: Path to the TXLF file
            
        Returns:
            Optional[ET.ElementTree]: Parsed XML tree or None if parsing fails
        """
        try:
            # Register namespaces
            for prefix, uri in self.namespaces.items():
                ET.register_namespace(prefix, uri)
            
            tree = ET.parse(file_path)
            logger.info(f"Successfully parsed TXLF file: {file_path}")
            return tree
        except Exception as e:
            logger.error(f"Error parsing TXLF file {file_path}: {e}")
            return None
    
    def extract_mt_suggestion_segments(self, tree: ET.ElementTree) -> List[Dict]:
        """
        Extract segments with state-qualifier="mt-suggestion" from the TXLF file.
        
        Args:
            tree: Parsed XML tree
            
        Returns:
            List[Dict]: List of segments with mt-suggestion state
        """
        segments = []
        
        try:
            root = tree.getroot()
            
            # Find all trans-unit elements
            for trans_unit in root.findall('.//xliff:trans-unit', self.namespaces):
                # Check if target has state-qualifier="mt-suggestion"
                target = trans_unit.find('xliff:target', self.namespaces)
                if target is not None:
                    state_qualifier = target.get('state-qualifier')
                    if state_qualifier == 'mt-suggestion':
                        # Extract source and target text
                        source_elem = trans_unit.find('xliff:source', self.namespaces)
                        source_text = source_elem.text if source_elem is not None else ""
                        target_text = target.text if target.text else ""
                        
                        # Get the trans-unit ID
                        unit_id = trans_unit.get('id', '')
                        
                        segments.append({
                            'id': unit_id,
                            'source_text': source_text,
                            'target_text': target_text,
                            'trans_unit': trans_unit,
                            'target_elem': target
                        })
            
            logger.info(f"Found {len(segments)} segments with mt-suggestion state")
            return segments
        except Exception as e:
            logger.error(f"Error extracting mt-suggestion segments: {e}")
            return []
    
    async def evaluate_segment(
        self, 
        source_text: str, 
        target_text: str, 
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ) -> Dict:
        """
        Evaluate a single segment using the LLM evaluator.
        
        Args:
            source_text: Source text
            target_text: Target text
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
            
        Returns:
            Dict: Evaluation result with score and other metrics
        """
        try:
            # Create a copy of the prompt template with segment variables
            segment_prompt = PromptTemplate(
                template_messages=prompt_template.template_messages,
                llm_configuration=prompt_template.llm_configuration,
                output_schema=prompt_template.output_schema,
                json_schema=prompt_template.json_schema
            )
            
            # Add segment variables
            segment_variables = [
                TemplateVariable(key="source_text", value=source_text),
                TemplateVariable(key="target_text", value=target_text)
            ]
            
            # Combine with existing variables
            existing_vars = {var.key: var.value for var in prompt_template.template_variables}
            for var in segment_variables:
                existing_vars[var.key] = var.value
                
            segment_prompt.template_variables = [
                TemplateVariable(key=k, value=v) for k, v in existing_vars.items()
            ]
            
            # Call LLM to evaluate the segment
            evaluation_result = await self.llm_evaluator.call_llm(segment_prompt, rag_configs or [])
            
            if isinstance(evaluation_result, dict):
                return evaluation_result
            else:
                logger.warning(f"Unexpected evaluation result format: {evaluation_result}")
                return self._generate_fallback_evaluation(source_text, target_text)
        except Exception as e:
            logger.error(f"Error evaluating segment: {e}")
            return self._generate_fallback_evaluation(source_text, target_text)
    
    def _generate_fallback_evaluation(self, source_text: str, target_text: str) -> Dict:
        """
        Generate a fallback evaluation when LLM evaluation fails.
        
        Args:
            source_text: Source text
            target_text: Target text
            
        Returns:
            Dict: Fallback evaluation result
        """
        # Simple heuristic-based evaluation
        score = 95  # Default to high quality
        error_category = "None"
        error_severity = ""
        comment = ""
        
        # Basic checks
        if not target_text.strip():
            score = 50
            error_category = "Missing Translation"
            error_severity = "critical"
            comment = "Target text is empty"
        elif len(source_text.split()) > len(target_text.split()) + 3:
            score = 85
            error_category = "Omission"
            error_severity = "minor"
            comment = "Target text appears to be missing content"
        elif len(target_text.split()) > len(source_text.split()) + 3:
            score = 90
            error_category = "Addition"
            error_severity = "minor"
            comment = "Target text appears to have additional content"
        
        return {
            "score": score,
            "error_category": error_category,
            "error_severity": error_severity,
            "comment": comment
        }
    
    def update_segment_with_evaluation(self, segment: Dict, evaluation: Dict) -> None:
        """
        Update a segment with evaluation results.
        
        Args:
            segment: Segment dictionary containing XML elements
            evaluation: Evaluation results
        """
        try:
            target_elem = segment['target_elem']
            score = evaluation.get('score', 95)
            
            # Add gs4tr:score attribute
            target_elem.set(f"{{{self.namespaces['gs4tr']}}}score", str(score))
            
            # If score >= 95, add gs4tr:locked="true"
            if score >= 95:
                target_elem.set(f"{{{self.namespaces['gs4tr']}}}locked", "true")
            
            logger.info(f"Updated segment {segment['id']} with score {score}")
        except Exception as e:
            logger.error(f"Error updating segment with evaluation: {e}")
    
    def check_all_segments_pass(self, segments: List[Dict], evaluations: List[Dict]) -> bool:
        """
        Check if all segments have scores >= 95.
        
        Args:
            segments: List of segments
            evaluations: List of evaluation results
            
        Returns:
            bool: True if all segments pass (score >= 95), False otherwise
        """
        try:
            for evaluation in evaluations:
                score = evaluation.get('score', 0)
                if score < 95:
                    return False
            return True
        except Exception as e:
            logger.error(f"Error checking segment scores: {e}")
            return False
    
    def add_qe_evaluation_pass(self, tree: ET.ElementTree) -> None:
        """
        Add gs4tr:qe-evaluation="pass" to the file element.
        
        Args:
            tree: XML tree
        """
        try:
            root = tree.getroot()
            file_elem = root.find('xliff:file', self.namespaces)
            
            if file_elem is not None:
                file_elem.set(f"{{{self.namespaces['gs4tr']}}}qe-evaluation", "pass")
                logger.info("Added qe-evaluation='pass' to file element")
            else:
                logger.warning("Could not find file element to add qe-evaluation attribute")
        except Exception as e:
            logger.error(f"Error adding qe-evaluation pass: {e}")
    
    def save_txlf_file(self, tree: ET.ElementTree, output_path: str) -> bool:
        """
        Save the modified TXLF file.
        
        Args:
            tree: Modified XML tree
            output_path: Path to save the file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write the XML file with proper encoding and declaration
            tree.write(output_path, encoding='utf-8', xml_declaration=True)
            logger.info(f"Saved TXLF file to: {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving TXLF file: {e}")
            return False
    
    async def process_txlf_file(
        self, 
        file_path: str, 
        output_path: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ) -> bool:
        """
        Process a complete TXLF file for translation quality evaluation.
        
        Args:
            file_path: Path to input TXLF file
            output_path: Path to save output TXLF file
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
            
        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            # Parse the TXLF file
            tree = self.parse_txlf_file(file_path)
            if not tree:
                return False
            
            # Extract mt-suggestion segments
            segments = self.extract_mt_suggestion_segments(tree)
            if not segments:
                logger.warning("No mt-suggestion segments found in the file")
                return False
            
            # Evaluate each segment
            evaluations = []
            for i, segment in enumerate(segments):
                logger.info(f"Evaluating segment {i+1}/{len(segments)}: {segment['id']}")
                
                evaluation = await self.evaluate_segment(
                    segment['source_text'],
                    segment['target_text'],
                    prompt_template,
                    rag_configs
                )
                
                evaluations.append(evaluation)
                
                # Update the segment with evaluation results
                self.update_segment_with_evaluation(segment, evaluation)
            
            # Check if all segments pass (score >= 95)
            if self.check_all_segments_pass(segments, evaluations):
                self.add_qe_evaluation_pass(tree)
                logger.info("All segments passed QE evaluation")
            else:
                logger.info("Some segments did not pass QE evaluation")
            
            # Save the modified file
            return self.save_txlf_file(tree, output_path)
            
        except Exception as e:
            logger.error(f"Error processing TXLF file: {e}")
            return False
