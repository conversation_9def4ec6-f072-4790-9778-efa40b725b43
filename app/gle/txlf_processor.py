"""
TXLF file processor for translation quality evaluation.
"""
import os
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple, Any

from loguru import logger

from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.schemas import PromptT<PERSON>plate, RAGConfig, TemplateVariable


class TXLFProcessor:
    """Processor for TXLF files to perform translation quality evaluation."""
    
    def __init__(self):
        self.llm_evaluator = LLMTranslationEvaluator()
        # Define XML namespaces
        self.namespaces = {
            'xliff': 'urn:oasis:names:tc:xliff:document:1.2',
            'gs4tr': 'http://www.gs4tr.org/schema/xliff-ext'
        }
    
    def parse_txlf_file(self, file_path: str) -> Optional[ET.ElementTree]:
        """
        Parse a TXLF file and return the XML tree.

        Args:
            file_path: Path to the TXLF file

        Returns:
            Optional[ET.ElementTree]: Parsed XML tree or None if parsing fails
        """
        try:
            # Parse without registering namespaces to preserve original structure
            tree = ET.parse(file_path)

            # Store the original XML content for reference
            with open(file_path, 'r', encoding='utf-8') as f:
                self.original_xml_content = f.read()

            logger.info(f"Successfully parsed TXLF file: {file_path}")
            return tree
        except Exception as e:
            logger.error(f"Error parsing TXLF file {file_path}: {e}")
            return None
    
    def extract_mt_suggestion_segments(self, tree: ET.ElementTree) -> List[Dict]:
        """
        Extract segments with state-qualifier="mt-suggestion" from the TXLF file.

        Args:
            tree: Parsed XML tree

        Returns:
            List[Dict]: List of segments with mt-suggestion state
        """
        segments = []

        try:
            root = tree.getroot()

            # Find all trans-unit elements (try multiple approaches)
            trans_units = []

            # Try with namespace first
            trans_units = root.findall('.//xliff:trans-unit', self.namespaces)

            # If no results, try without namespace
            if not trans_units:
                for elem in root.iter():
                    if elem.tag.endswith('trans-unit'):
                        trans_units.append(elem)

            logger.info(f"Found {len(trans_units)} trans-unit elements")

            for trans_unit in trans_units:
                # Check if target has state-qualifier="mt-suggestion"
                target = None

                # Try with namespace first
                target = trans_unit.find('xliff:target', self.namespaces)

                # If not found, try without namespace
                if target is None:
                    for elem in trans_unit.iter():
                        if elem.tag.endswith('target'):
                            target = elem
                            break

                if target is not None:
                    state_qualifier = target.get('state-qualifier')
                    if state_qualifier == 'mt-suggestion':
                        # Extract source text (try multiple approaches)
                        source_elem = None
                        source_text = ""

                        # Try with namespace
                        source_elem = trans_unit.find('xliff:source', self.namespaces)

                        # If not found, try without namespace
                        if source_elem is None:
                            for elem in trans_unit.iter():
                                if elem.tag.endswith('source'):
                                    source_elem = elem
                                    break

                        if source_elem is not None and source_elem.text is not None:
                            source_text = str(source_elem.text).strip()

                        # Extract target text
                        target_text = ""
                        if target is not None and target.text is not None:
                            target_text = str(target.text).strip()

                        # Get the trans-unit ID
                        unit_id = trans_unit.get('id', '')

                        segments.append({
                            'id': unit_id,
                            'source_text': source_text,
                            'target_text': target_text,
                            'trans_unit': trans_unit,
                            'target_elem': target
                        })

            logger.info(f"Found {len(segments)} segments with mt-suggestion state")
            return segments
        except Exception as e:
            logger.error(f"Error extracting mt-suggestion segments: {e}")
            return []
    
    async def evaluate_segment(
        self,
        source_text: str,
        target_text: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ) -> Dict:
        """
        Evaluate a single segment using the LLM evaluator.

        Args:
            source_text: Source text
            target_text: Target text
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations

        Returns:
            Dict: Evaluation result with score and other metrics
        """
        try:
            # Ensure we have valid string values (handle None values)
            source_text = str(source_text) if source_text is not None else ""
            target_text = str(target_text) if target_text is not None else ""

            logger.info(f"Evaluating segment - Source: '{source_text[:50]}...', Target: '{target_text[:50]}...'")

            # Create a copy of the prompt template with segment variables
            segment_prompt = PromptTemplate(
                template_messages=prompt_template.template_messages,
                llm_configuration=prompt_template.llm_configuration,
                output_schema=prompt_template.output_schema,
                json_schema=prompt_template.json_schema
            )

            # Add segment variables with validated string values
            segment_variables = [
                TemplateVariable(key="source_text", value=source_text),
                TemplateVariable(key="target_text", value=target_text)
            ]

            # Combine with existing variables, ensuring all values are strings
            existing_vars = {}
            for var in prompt_template.template_variables:
                # Ensure the value is a string
                value = str(var.value) if var.value is not None else ""
                existing_vars[var.key] = value

            for var in segment_variables:
                existing_vars[var.key] = var.value

            segment_prompt.template_variables = [
                TemplateVariable(key=k, value=str(v) if v is not None else "")
                for k, v in existing_vars.items()
            ]

            # Call LLM to evaluate the segment
            logger.info("Calling LLM evaluator...")
            evaluation_result = await self.llm_evaluator.call_llm(segment_prompt, rag_configs or [])

            if isinstance(evaluation_result, dict):
                logger.info(f"LLM evaluation successful: score={evaluation_result.get('score', 'N/A')}")
                return evaluation_result
            else:
                logger.warning(f"Unexpected evaluation result format: {evaluation_result}")
                logger.info("Using fallback evaluation due to unexpected result format")
                return self._generate_fallback_evaluation(source_text, target_text)

        except Exception as e:
            logger.error(f"Error evaluating segment: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Source text: {source_text}")
            logger.error(f"Target text: {target_text}")

            # Check if it's a RAG-related error
            if "milvus" in str(e).lower() or "search" in str(e).lower():
                logger.warning("RAG/Milvus error detected, using fallback evaluation")
            else:
                logger.warning("General evaluation error, using fallback evaluation")

            return self._generate_fallback_evaluation(source_text, target_text)
    
    def _generate_fallback_evaluation(self, source_text: str, target_text: str) -> Dict:
        """
        Generate a fallback evaluation when LLM evaluation fails.

        Args:
            source_text: Source text
            target_text: Target text

        Returns:
            Dict: Fallback evaluation result
        """
        # Ensure we have valid string values
        source_text = str(source_text) if source_text is not None else ""
        target_text = str(target_text) if target_text is not None else ""

        # Simple heuristic-based evaluation
        score = 95  # Default to high quality
        error_category = "None"
        error_severity = ""
        comment = ""

        # Basic checks
        if not target_text.strip():
            score = 50
            error_category = "Missing Translation"
            error_severity = "critical"
            comment = "Target text is empty"
        elif not source_text.strip():
            score = 60
            error_category = "Missing Source"
            error_severity = "critical"
            comment = "Source text is empty"
        elif len(source_text.split()) > len(target_text.split()) + 3:
            score = 85
            error_category = "Omission"
            error_severity = "minor"
            comment = "Target text appears to be missing content"
        elif len(target_text.split()) > len(source_text.split()) + 3:
            score = 90
            error_category = "Addition"
            error_severity = "minor"
            comment = "Target text appears to have additional content"

        return {
            "score": score,
            "error_category": error_category,
            "error_severity": error_severity,
            "comment": comment
        }
    
    def update_segment_with_evaluation(self, segment: Dict, evaluation: Dict) -> None:
        """
        Update a segment with evaluation results.

        Args:
            segment: Segment dictionary containing XML elements
            evaluation: Evaluation results
        """
        try:
            target_elem = segment['target_elem']
            trans_unit_elem = segment['trans_unit']  # Get the trans-unit element
            score = evaluation.get('score', 95)

            # Detect the gs4tr namespace prefix from the original file content
            gs4tr_prefix = self._detect_gs4tr_prefix_from_original()

            # Add gs4tr:score attribute to target element
            if gs4tr_prefix:
                target_elem.set(f"{gs4tr_prefix}:score", str(score))

                # If score >= 95, add gs4tr:locked="true" to trans-unit element (not target)
                if score >= 95:
                    trans_unit_elem.set(f"{gs4tr_prefix}:locked", "true")
            else:
                # Use simple attribute names without namespace prefix
                target_elem.set("score", str(score))
                if score >= 95:
                    trans_unit_elem.set("locked", "true")

            logger.info(f"Updated segment {segment['id']} with score {score} (locked: {score >= 95})")
        except Exception as e:
            logger.error(f"Error updating segment with evaluation: {e}")

    def _detect_gs4tr_prefix_from_original(self) -> Optional[str]:
        """
        Detect the gs4tr namespace prefix from the original file content.

        Returns:
            Optional[str]: The gs4tr prefix if found, None otherwise
        """
        try:
            if not hasattr(self, 'original_xml_content'):
                return 'gs4tr'  # Default fallback

            import re

            # Look for xmlns declarations with gs4tr
            xmlns_pattern = r'xmlns:([^=]+)="[^"]*gs4tr[^"]*"'
            matches = re.findall(xmlns_pattern, self.original_xml_content)

            if matches:
                # Return the first found prefix
                return matches[0]

            # Look for existing gs4tr attributes in the content
            attr_pattern = r'([a-zA-Z0-9_]+):(?:score|locked|segmentId)'
            matches = re.findall(attr_pattern, self.original_xml_content)

            for match in matches:
                # Common gs4tr prefixes
                if match in ['gs4tr', 'gs', 'g4tr', 'gs4']:
                    return match

            # Default fallback
            return 'gs4tr'

        except Exception as e:
            logger.warning(f"Could not detect gs4tr prefix from original: {e}")
            return 'gs4tr'
    
    def check_all_segments_pass(self, segments: List[Dict], evaluations: List[Dict]) -> bool:
        """
        Check if all segments have scores >= 95.
        
        Args:
            segments: List of segments
            evaluations: List of evaluation results
            
        Returns:
            bool: True if all segments pass (score >= 95), False otherwise
        """
        try:
            for evaluation in evaluations:
                score = evaluation.get('score', 0)
                if score < 95:
                    return False
            return True
        except Exception as e:
            logger.error(f"Error checking segment scores: {e}")
            return False
    
    def add_qe_evaluation_pass(self, tree: ET.ElementTree) -> None:
        """
        Add gs4tr:qe-evaluation="pass" to the file element.

        Args:
            tree: XML tree
        """
        try:
            root = tree.getroot()

            # Try to find file element with different approaches
            file_elem = None

            # First try with namespace
            file_elem = root.find('xliff:file', self.namespaces)

            # If not found, try without namespace (in case it's the root)
            if file_elem is None:
                if root.tag.endswith('file'):
                    file_elem = root
                else:
                    # Try to find any file element
                    for elem in root.iter():
                        if elem.tag.endswith('file'):
                            file_elem = elem
                            break

            if file_elem is not None:
                # Detect gs4tr prefix from original content
                gs4tr_prefix = self._detect_gs4tr_prefix_from_original()

                # Add qe-evaluation attribute
                if gs4tr_prefix:
                    file_elem.set(f"{gs4tr_prefix}:qe-evaluation", "pass")
                else:
                    file_elem.set("qe-evaluation", "pass")

                logger.info("Added qe-evaluation='pass' to file element")
            else:
                logger.warning("Could not find file element to add qe-evaluation attribute")
        except Exception as e:
            logger.error(f"Error adding qe-evaluation pass: {e}")
    
    def save_txlf_file(self, tree: ET.ElementTree, output_path: str) -> bool:
        """
        Save the modified TXLF file preserving original XML structure exactly.

        Args:
            tree: Modified XML tree
            output_path: Path to save the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            if hasattr(self, 'original_xml_content'):
                # Use string manipulation to preserve exact structure
                modified_content = self._preserve_original_structure_with_modifications(tree)

                # Write the file preserving exact structure
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
            else:
                # Fallback to standard XML writing
                tree.write(output_path, encoding='utf-8', xml_declaration=True)

            logger.info(f"Saved TXLF file to: {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving TXLF file: {e}")
            return False

    def _preserve_original_structure_with_modifications(self, tree: ET.ElementTree) -> str:
        """
        Preserve the original XML structure while adding only the evaluation attributes.

        Args:
            tree: Modified XML tree

        Returns:
            str: Modified XML content preserving original structure
        """
        try:
            # Start with the original content
            modified_content = self.original_xml_content

            # Find all target elements that were modified and update them in the string
            root = tree.getroot()

            # Find all trans-unit elements
            trans_units = []
            trans_units = root.findall('.//xliff:trans-unit', self.namespaces)

            # If no results, try without namespace
            if not trans_units:
                for elem in root.iter():
                    if elem.tag.endswith('trans-unit'):
                        trans_units.append(elem)

            for trans_unit in trans_units:
                unit_id = trans_unit.get('id', '')

                # Find target with mt-suggestion
                target = trans_unit.find('xliff:target[@state-qualifier="mt-suggestion"]', self.namespaces)
                if target is None:
                    # Try without namespace
                    for elem in trans_unit.iter():
                        if (elem.tag.endswith('target') and
                            elem.get('state-qualifier') == 'mt-suggestion'):
                            target = elem
                            break

                if target is not None:
                    # Check if target has score attribute
                    score_attr = None
                    for attr_name, attr_value in target.attrib.items():
                        if attr_name.endswith(':score') or attr_name == 'score':
                            score_attr = (attr_name, attr_value)
                            break

                    # Check if trans-unit has locked attribute
                    locked_attr = None
                    for attr_name, attr_value in trans_unit.attrib.items():
                        if attr_name.endswith(':locked') or attr_name == 'locked':
                            locked_attr = (attr_name, attr_value)
                            break

                    if score_attr or locked_attr:
                        # Update both target and trans-unit elements in the original content
                        modified_content = self._update_segment_in_content(
                            modified_content, unit_id, score_attr, locked_attr
                        )

            # Check if qe-evaluation="pass" was added to file element
            file_elem = root.find('xliff:file', self.namespaces)
            if file_elem is None:
                if root.tag.endswith('file'):
                    file_elem = root
                else:
                    for elem in root.iter():
                        if elem.tag.endswith('file'):
                            file_elem = elem
                            break

            if file_elem is not None:
                # Check if qe-evaluation attribute exists
                qe_eval_attr = None
                for attr_name, attr_value in file_elem.attrib.items():
                    if attr_name.endswith(':qe-evaluation') or attr_name == 'qe-evaluation':
                        if attr_value == 'pass':
                            qe_eval_attr = (attr_name, attr_value)
                            break

                if qe_eval_attr:
                    # Add qe-evaluation to file element
                    modified_content = self._add_qe_evaluation_to_content(modified_content, qe_eval_attr)

            return modified_content

        except Exception as e:
            logger.error(f"Error preserving original structure: {e}")
            # Fallback to original content
            return self.original_xml_content

    def _update_segment_in_content(self, content: str, unit_id: str, score_attr: tuple, locked_attr: tuple) -> str:
        """
        Update both trans-unit and target elements in the original content string.

        Args:
            content: Original XML content
            unit_id: Trans-unit ID
            score_attr: Score attribute tuple (name, value) - goes on target
            locked_attr: Locked attribute tuple (name, value) - goes on trans-unit

        Returns:
            str: Updated content
        """
        try:
            import re

            # Find the trans-unit with the specific ID
            trans_unit_pattern = rf'<trans-unit[^>]*id="{re.escape(unit_id)}"[^>]*>'
            trans_unit_match = re.search(trans_unit_pattern, content)

            if trans_unit_match:
                original_trans_unit_tag = trans_unit_match.group(0)

                # Add locked attribute to trans-unit if not already present
                if locked_attr and locked_attr[0] not in original_trans_unit_tag:
                    new_trans_unit_tag = original_trans_unit_tag[:-1] + f' {locked_attr[0]}="{locked_attr[1]}">'
                    content = content.replace(original_trans_unit_tag, new_trans_unit_tag)

                # Find the target element within this trans-unit
                start_pos = trans_unit_match.start()

                # Find the end of this trans-unit
                trans_unit_end_pattern = r'</trans-unit>'
                trans_unit_end_match = re.search(trans_unit_end_pattern, content[start_pos:])

                if trans_unit_end_match:
                    end_pos = start_pos + trans_unit_end_match.end()
                    trans_unit_content = content[start_pos:end_pos]

                    # Find target with state-qualifier="mt-suggestion"
                    target_pattern = r'<target[^>]*state-qualifier="mt-suggestion"[^>]*>'
                    target_match = re.search(target_pattern, trans_unit_content)

                    if target_match:
                        original_target_tag = target_match.group(0)

                        # Add score attribute to target if not already present
                        if score_attr and score_attr[0] not in original_target_tag:
                            new_target_tag = original_target_tag[:-1] + f' {score_attr[0]}="{score_attr[1]}">'
                            content = content.replace(original_target_tag, new_target_tag)

            return content

        except Exception as e:
            logger.error(f"Error updating segment in content: {e}")
            return content

    def _add_qe_evaluation_to_content(self, content: str, qe_eval_attr: tuple) -> str:
        """
        Add qe-evaluation attribute to file element in content.

        Args:
            content: Original XML content
            qe_eval_attr: QE evaluation attribute tuple (name, value)

        Returns:
            str: Updated content
        """
        try:
            import re

            # Find the file element
            file_pattern = r'<file[^>]*>'
            file_match = re.search(file_pattern, content)

            if file_match:
                original_file_tag = file_match.group(0)

                # Add qe-evaluation attribute if not already present
                if qe_eval_attr[0] not in original_file_tag:
                    # Insert before the closing >
                    new_file_tag = original_file_tag[:-1] + f' {qe_eval_attr[0]}="{qe_eval_attr[1]}">'
                    content = content.replace(original_file_tag, new_file_tag)

            return content

        except Exception as e:
            logger.error(f"Error adding qe-evaluation to content: {e}")
            return content
    
    async def process_txlf_file(
        self,
        file_path: str,
        output_path: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ) -> Dict[str, Any]:
        """
        Process a complete TXLF file for translation quality evaluation.

        Args:
            file_path: Path to input TXLF file
            output_path: Path to save output TXLF file
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations

        Returns:
            Dict[str, Any]: Processing results with detailed information
        """
        result = {
            "success": False,
            "segments_processed": 0,
            "segments_passed": 0,
            "all_segments_passed": False,
            "output_path": output_path,
            "evaluations": [],
            "error": None
        }

        try:
            # Parse the TXLF file
            tree = self.parse_txlf_file(file_path)
            if not tree:
                result["error"] = "Failed to parse TXLF file"
                return result

            # Extract mt-suggestion segments
            segments = self.extract_mt_suggestion_segments(tree)
            if not segments:
                logger.warning("No mt-suggestion segments found in the file")
                result["error"] = "No mt-suggestion segments found"
                return result

            # Evaluate each segment
            evaluations = []
            segments_passed = 0

            for i, segment in enumerate(segments):
                logger.info(f"Evaluating segment {i+1}/{len(segments)}: {segment['id']}")

                evaluation = await self.evaluate_segment(
                    segment['source_text'],
                    segment['target_text'],
                    prompt_template,
                    rag_configs
                )

                evaluations.append(evaluation)

                # Update the segment with evaluation results
                self.update_segment_with_evaluation(segment, evaluation)

                # Count passed segments
                if evaluation.get('score', 0) >= 95:
                    segments_passed += 1

            result["segments_processed"] = len(segments)
            result["segments_passed"] = segments_passed
            result["evaluations"] = evaluations

            # Check if all segments pass (score >= 95)
            all_passed = self.check_all_segments_pass(segments, evaluations)
            result["all_segments_passed"] = all_passed

            if all_passed:
                self.add_qe_evaluation_pass(tree)
                logger.info("All segments passed QE evaluation")
            else:
                logger.info(f"QE evaluation completed: {segments_passed}/{len(segments)} segments passed")

            # Save the modified file
            if self.save_txlf_file(tree, output_path):
                result["success"] = True
                logger.info(f"Successfully processed TXLF file: {output_path}")
            else:
                result["error"] = "Failed to save processed TXLF file"

            return result

        except Exception as e:
            logger.error(f"Error processing TXLF file: {e}")
            result["error"] = str(e)
            return result
