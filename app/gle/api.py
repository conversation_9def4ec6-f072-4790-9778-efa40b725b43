"""
API endpoints for GLE integration.
"""
import os
from typing import List, Optional

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from loguru import logger
from pydantic import BaseModel
from starlette import status

from app.gle.workflow import <PERSON><PERSON><PERSON>orkflow
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig


# Request/Response models
class GLEWorkflowRequest(BaseModel):
    prompt_template: PromptTemplate
    rag_configs: List[RAGConfig] = []
    output_dir: Optional[str] = None


class GLEWorkflowResponse(BaseModel):
    success: bool
    submissions_processed: int
    files_processed: int
    files_passed: int
    files_uploaded: int
    phases_completed: int
    output_files: List[str]
    errors: List[str]


class GLESingleSubmissionRequest(BaseModel):
    submission_id: int
    prompt_template: PromptTemplate
    rag_configs: List[RAGConfig] = []
    output_dir: Optional[str] = None


class GLESingleSubmissionResponse(BaseModel):
    success: bool
    submission_id: int
    files_processed: int
    files_passed: int
    files_uploaded: int
    phase_completed: bool
    output_files: List[str]
    errors: List[str]


class GLESubmissionInfo(BaseModel):
    submission_id: int
    submission_name: str
    languages: List[dict]


class GLEClaimableSubmissionsResponse(BaseModel):
    submissions: List[GLESubmissionInfo]


class GLEUploadRequest(BaseModel):
    submission_id: int
    file_path: str
    original_filename: str


class GLEUploadResponse(BaseModel):
    success: bool
    submission_id: int
    filename: str
    message: str


class GLECompletePhaseRequest(BaseModel):
    submission_id: int
    target_language: Optional[str] = None  # If not provided, will get all targets


class GLECompletePhaseResponse(BaseModel):
    success: bool
    submission_id: int
    target_ids: List[int]
    message: str


# Router
gle_api_router = APIRouter(prefix="/gle")


@gle_api_router.post("/workflow/run", response_model=GLEWorkflowResponse)
async def run_gle_workflow(
    background_tasks: BackgroundTasks,
    request: GLEWorkflowRequest
):
    """
    Run the complete GLE workflow to process all claimable submissions.
    
    This endpoint will:
    1. Authenticate with GLE
    2. Get claimable submissions
    3. Claim the submissions
    4. Download and process TXLF files
    5. Perform translation quality evaluation
    6. Save updated TXLF files
    7. Upload processed files back to GLE
    8. Complete the API_QE phase for each submission
    """
    try:
        workflow = GLEWorkflow()
        
        # Set default output directory if not provided
        output_dir = request.output_dir or "/tmp/gle_workflow_output"
        
        logger.info("Starting GLE workflow via API")
        
        # Run the workflow
        results = await workflow.run_complete_workflow(
            prompt_template=request.prompt_template,
            rag_configs=request.rag_configs,
            output_dir=output_dir
        )
        
        return GLEWorkflowResponse(**results)
        
    except Exception as e:
        logger.error(f"Error in GLE workflow API: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running GLE workflow: {str(e)}"
        )


@gle_api_router.post("/submission/process", response_model=GLESingleSubmissionResponse)
async def process_single_submission(
    background_tasks: BackgroundTasks,
    request: GLESingleSubmissionRequest
):
    """
    Process a single submission by ID.
    
    This endpoint will:
    1. Authenticate with GLE
    2. Download the specified submission
    3. Process TXLF files
    4. Perform translation quality evaluation
    5. Save updated TXLF files
    6. Upload processed files back to GLE
    7. Complete the API_QE phase for the submission
    """
    try:
        workflow = GLEWorkflow()
        
        # Set default output directory if not provided
        output_dir = request.output_dir or f"/tmp/gle_submission_{request.submission_id}"
        
        logger.info(f"Processing single submission {request.submission_id} via API")
        
        # Process the submission
        results = await workflow.process_single_submission(
            submission_id=request.submission_id,
            prompt_template=request.prompt_template,
            rag_configs=request.rag_configs,
            output_dir=output_dir
        )
        
        return GLESingleSubmissionResponse(**results)
        
    except Exception as e:
        logger.error(f"Error processing single submission: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing submission: {str(e)}"
        )


@gle_api_router.get("/submissions/claimable", response_model=GLEClaimableSubmissionsResponse)
async def get_claimable_submissions():
    """
    Get all claimable submissions for API_QE phase.
    
    Returns a list of submissions that can be claimed for quality evaluation.
    """
    try:
        from app.gle.service import GLEService
        
        gle_service = GLEService()
        
        # Authenticate
        if not await gle_service.authenticate():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with GLE system"
            )
        
        # Get claimable submissions
        submissions = await gle_service.get_claimable_submissions()
        
        # Convert to response format
        submission_info = []
        for sub in submissions:
            submission_info.append(GLESubmissionInfo(
                submission_id=sub.get("submissionId"),
                submission_name=sub.get("submissionName", ""),
                languages=sub.get("languages", [])
            ))
        
        return GLEClaimableSubmissionsResponse(submissions=submission_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting claimable submissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting claimable submissions: {str(e)}"
        )


@gle_api_router.get("/files/download/{file_path:path}")
async def download_processed_file(file_path: str):
    """
    Download a processed TXLF file.
    
    Args:
        file_path: Path to the file to download (relative to output directory)
    """
    try:
        # Security check: ensure the file path is within allowed directories
        if not file_path.startswith("/tmp/gle"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access to this file path is not allowed"
            )
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        return FileResponse(
            path=file_path,
            filename=os.path.basename(file_path),
            media_type="application/xml"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error downloading file: {str(e)}"
        )


@gle_api_router.post("/upload", response_model=GLEUploadResponse)
async def upload_file_to_gle(request: GLEUploadRequest):
    """
    Upload a processed TXLF file back to GLE.

    Args:
        request: Upload request with submission ID, file path, and original filename
    """
    try:
        from app.gle.service import GLEService

        gle_service = GLEService()

        # Authenticate
        if not await gle_service.authenticate():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with GLE system"
            )

        # Upload the file
        success = await gle_service.upload_txlf_file(
            request.submission_id,
            request.file_path,
            request.original_filename
        )

        return GLEUploadResponse(
            success=success,
            submission_id=request.submission_id,
            filename=request.original_filename,
            message="File uploaded successfully" if success else "File upload failed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file to GLE: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading file: {str(e)}"
        )


@gle_api_router.post("/complete-phase", response_model=GLECompletePhaseResponse)
async def complete_gle_phase(request: GLECompletePhaseRequest):
    """
    Complete the API_QE phase for a submission.

    Args:
        request: Phase completion request with submission ID and optional target language
    """
    try:
        from app.gle.service import GLEService

        gle_service = GLEService()

        # Authenticate
        if not await gle_service.authenticate():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with GLE system"
            )

        # Get target IDs
        if request.target_language:
            target_ids = await gle_service.get_target_ids(request.submission_id, request.target_language)
        else:
            # Get all target IDs for the submission (you might need to implement this)
            # For now, we'll try common languages
            all_target_ids = []
            common_languages = ["de", "es", "fr", "it", "pt", "zh", "ja", "ko"]
            for lang in common_languages:
                target_ids = await gle_service.get_target_ids(request.submission_id, lang)
                all_target_ids.extend(target_ids)
            target_ids = list(set(all_target_ids))  # Remove duplicates

        if not target_ids:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No target IDs found for the specified submission and language"
            )

        # Complete the phase
        success = await gle_service.complete_phase(request.submission_id, target_ids)

        return GLECompletePhaseResponse(
            success=success,
            submission_id=request.submission_id,
            target_ids=target_ids,
            message="Phase completed successfully" if success else "Phase completion failed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing GLE phase: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error completing phase: {str(e)}"
        )


@gle_api_router.get("/submissions/{submission_id}/targets")
async def get_submission_targets(submission_id: int, target_language: Optional[str] = None):
    """
    Get target IDs for a submission and language.

    Args:
        submission_id: The submission ID
        target_language: Optional target language code
    """
    try:
        from app.gle.service import GLEService

        gle_service = GLEService()

        # Authenticate
        if not await gle_service.authenticate():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with GLE system"
            )

        if target_language:
            target_ids = await gle_service.get_target_ids(submission_id, target_language)
        else:
            # Return info that target_language is required
            return {
                "message": "target_language parameter is required",
                "example": f"/gle/submissions/{submission_id}/targets?target_language=de"
            }

        return {
            "submission_id": submission_id,
            "target_language": target_language,
            "target_ids": target_ids,
            "count": len(target_ids)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting submission targets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting targets: {str(e)}"
        )


@gle_api_router.get("/health")
async def health_check():
    """
    Health check endpoint for GLE integration.
    """
    try:
        from app.gle.service import GLEService

        gle_service = GLEService()

        # Test authentication
        auth_success = await gle_service.authenticate()

        return {
            "status": "healthy" if auth_success else "unhealthy",
            "gle_authentication": "success" if auth_success else "failed",
            "message": "GLE integration is working" if auth_success else "GLE authentication failed"
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "gle_authentication": "failed",
            "message": f"Health check failed: {str(e)}"
        }
