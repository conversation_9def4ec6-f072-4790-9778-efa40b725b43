"""
GLE workflow orchestrator for end-to-end translation quality evaluation.
"""
import asyncio
import os
import tempfile
from typing import Dict, List, Optional

from loguru import logger

from app.gle.service import GLEService
from app.gle.txlf_processor import TXLFProcessor
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig


class GLEWorkflow:
    """Orchestrates the complete GLE translation quality evaluation workflow."""
    
    def __init__(self):
        self.gle_service = GLEService()
        self.txlf_processor = TXLFProcessor()
    
    async def run_complete_workflow(
        self,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None,
        output_dir: str = "/tmp/gle_output"
    ) -> Dict:
        """
        Run the complete GLE workflow.
        
        Args:
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
            output_dir: Directory to save output files
            
        Returns:
            Dict: Workflow results
        """
        results = {
            "success": False,
            "submissions_processed": 0,
            "files_processed": 0,
            "files_passed": 0,
            "output_files": [],
            "errors": []
        }
        
        try:
            logger.info("Starting GLE workflow")
            
            # Step 1: Authenticate
            if not await self.gle_service.authenticate():
                results["errors"].append("Authentication failed")
                return results
            
            # Step 2: Get claimable submissions
            submissions = await self.gle_service.get_claimable_submissions()
            if not submissions:
                results["errors"].append("No claimable submissions found")
                return results
            
            logger.info(f"Found {len(submissions)} claimable submissions")
            
            # Step 3: Claim submissions
            if not await self.gle_service.claim_submissions(submissions):
                results["errors"].append("Failed to claim submissions")
                return results
            
            # Step 4: Process each submission
            for submission in submissions:
                submission_id = submission.get("submissionId")
                submission_name = submission.get("submissionName", f"submission_{submission_id}")
                
                try:
                    logger.info(f"Processing submission {submission_id}: {submission_name}")
                    
                    # Request download
                    download_id = await self.gle_service.request_download(submission_id)
                    if not download_id:
                        results["errors"].append(f"Failed to request download for submission {submission_id}")
                        continue
                    
                    # Wait for download to be ready
                    max_wait_time = 300  # 5 minutes
                    wait_interval = 10   # 10 seconds
                    waited_time = 0
                    
                    while waited_time < max_wait_time:
                        if await self.gle_service.check_download_status(submission_id, download_id):
                            break
                        await asyncio.sleep(wait_interval)
                        waited_time += wait_interval
                    else:
                        results["errors"].append(f"Download timeout for submission {submission_id}")
                        continue
                    
                    # Download the file
                    temp_dir = tempfile.mkdtemp()
                    zip_path = await self.gle_service.download_file(download_id, temp_dir)
                    if not zip_path:
                        results["errors"].append(f"Failed to download file for submission {submission_id}")
                        continue
                    
                    # Extract TXLF files
                    extract_dir = os.path.join(temp_dir, "extracted")
                    txlf_files = self.gle_service.extract_txlf_files(zip_path, extract_dir)
                    if not txlf_files:
                        results["errors"].append(f"No TXLF files found in submission {submission_id}")
                        continue
                    
                    # Process each TXLF file
                    submission_output_dir = os.path.join(output_dir, f"submission_{submission_id}")
                    os.makedirs(submission_output_dir, exist_ok=True)

                    submission_files_processed = 0
                    submission_files_passed = 0
                    processed_files = []  # Track processed files for upload

                    for txlf_file_info in txlf_files:
                        try:
                            file_path = txlf_file_info['full_path']
                            original_name = txlf_file_info['original_name']
                            output_file_path = os.path.join(submission_output_dir, original_name)

                            logger.info(f"Processing TXLF file: {original_name}")

                            # Process the TXLF file
                            process_result = await self.txlf_processor.process_txlf_file(
                                file_path,
                                output_file_path,
                                prompt_template,
                                rag_configs
                            )

                            if process_result["success"]:
                                submission_files_processed += 1
                                results["output_files"].append(output_file_path)

                                # Track file for upload
                                processed_files.append({
                                    'output_path': output_file_path,
                                    'original_name': original_name,
                                    'all_passed': process_result["all_segments_passed"]
                                })

                                # Check if file passed
                                if process_result["all_segments_passed"]:
                                    submission_files_passed += 1
                                    results["files_passed"] += 1

                                logger.info(f"Successfully processed {original_name}: "
                                          f"{process_result['segments_passed']}/{process_result['segments_processed']} segments passed")
                            else:
                                error_msg = process_result.get("error", "Unknown error")
                                results["errors"].append(f"Failed to process {original_name}: {error_msg}")

                        except Exception as e:
                            logger.error(f"Error processing TXLF file {txlf_file_info['original_name']}: {e}")
                            results["errors"].append(f"Error processing {txlf_file_info['original_name']}: {str(e)}")

                    # Step 6: Upload processed files back to GLE
                    upload_success_count = 0
                    for file_info in processed_files:
                        try:
                            logger.info(f"Uploading file {file_info['original_name']} to submission {submission_id}")
                            upload_success = await self.gle_service.upload_txlf_file(
                                submission_id,
                                file_info['output_path'],
                                file_info['original_name']
                            )

                            if upload_success:
                                upload_success_count += 1
                                logger.info(f"Successfully uploaded {file_info['original_name']}")
                            else:
                                results["errors"].append(f"Failed to upload {file_info['original_name']}")

                        except Exception as e:
                            logger.error(f"Error uploading file {file_info['original_name']}: {e}")
                            results["errors"].append(f"Error uploading {file_info['original_name']}: {str(e)}")

                    # Step 7: Complete the API_QE phase if all files were uploaded successfully
                    if upload_success_count == len(processed_files) and processed_files:
                        try:
                            # Get target IDs for completion
                            # We need to get target languages from the submission
                            target_languages = set()
                            for lang_info in submission.get("languages", []):
                                target_lang = lang_info.get("targetLanguageCode")
                                if target_lang:
                                    target_languages.add(target_lang)

                            all_target_ids = []
                            for target_lang in target_languages:
                                target_ids = await self.gle_service.get_target_ids(submission_id, target_lang)
                                all_target_ids.extend(target_ids)

                            if all_target_ids:
                                logger.info(f"Completing API_QE phase for submission {submission_id} with target IDs: {all_target_ids}")
                                completion_success = await self.gle_service.complete_phase(submission_id, all_target_ids)

                                if completion_success:
                                    logger.info(f"Successfully completed API_QE phase for submission {submission_id}")
                                else:
                                    results["errors"].append(f"Failed to complete API_QE phase for submission {submission_id}")
                            else:
                                results["errors"].append(f"No target IDs found for submission {submission_id}")

                        except Exception as e:
                            logger.error(f"Error completing phase for submission {submission_id}: {e}")
                            results["errors"].append(f"Error completing phase for submission {submission_id}: {str(e)}")
                    else:
                        logger.warning(f"Skipping phase completion for submission {submission_id} due to upload failures")

                    results["files_processed"] += submission_files_processed
                    results["submissions_processed"] += 1

                    logger.info(f"Completed submission {submission_id}: "
                              f"{submission_files_processed} files processed, "
                              f"{submission_files_passed} files passed, "
                              f"{upload_success_count} files uploaded")
                
                except Exception as e:
                    logger.error(f"Error processing submission {submission_id}: {e}")
                    results["errors"].append(f"Error processing submission {submission_id}: {str(e)}")
            
            # Mark as successful if we processed at least one submission
            if results["submissions_processed"] > 0:
                results["success"] = True
            
            logger.info(f"GLE workflow completed: {results['submissions_processed']} submissions processed, "
                       f"{results['files_processed']} files processed, {results['files_passed']} files passed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in GLE workflow: {e}")
            results["errors"].append(f"Workflow error: {str(e)}")
            return results
    
    def _check_file_passed(self, file_path: str) -> bool:
        """
        Check if a TXLF file has qe-evaluation="pass".
        
        Args:
            file_path: Path to the TXLF file
            
        Returns:
            bool: True if file passed, False otherwise
        """
        try:
            tree = self.txlf_processor.parse_txlf_file(file_path)
            if not tree:
                return False
            
            root = tree.getroot()
            file_elem = root.find('xliff:file', self.txlf_processor.namespaces)
            
            if file_elem is not None:
                qe_evaluation = file_elem.get(f"{{{self.txlf_processor.namespaces['gs4tr']}}}qe-evaluation")
                return qe_evaluation == "pass"
            
            return False
        except Exception as e:
            logger.error(f"Error checking if file passed: {e}")
            return False
    
    async def process_single_submission(
        self,
        submission_id: int,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None,
        output_dir: str = "/tmp/gle_output"
    ) -> Dict:
        """
        Process a single submission by ID.
        
        Args:
            submission_id: The submission ID to process
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
            output_dir: Directory to save output files
            
        Returns:
            Dict: Processing results
        """
        results = {
            "success": False,
            "submission_id": submission_id,
            "files_processed": 0,
            "files_passed": 0,
            "output_files": [],
            "errors": []
        }
        
        try:
            logger.info(f"Processing single submission: {submission_id}")
            
            # Authenticate
            if not await self.gle_service.authenticate():
                results["errors"].append("Authentication failed")
                return results
            
            # Request download
            download_id = await self.gle_service.request_download(submission_id)
            if not download_id:
                results["errors"].append(f"Failed to request download for submission {submission_id}")
                return results
            
            # Wait for download to be ready
            max_wait_time = 300  # 5 minutes
            wait_interval = 10   # 10 seconds
            waited_time = 0
            
            while waited_time < max_wait_time:
                if await self.gle_service.check_download_status(submission_id, download_id):
                    break
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval
            else:
                results["errors"].append(f"Download timeout for submission {submission_id}")
                return results
            
            # Download and process files
            temp_dir = tempfile.mkdtemp()
            zip_path = await self.gle_service.download_file(download_id, temp_dir)
            if not zip_path:
                results["errors"].append(f"Failed to download file for submission {submission_id}")
                return results
            
            # Extract and process TXLF files
            extract_dir = os.path.join(temp_dir, "extracted")
            txlf_files = self.gle_service.extract_txlf_files(zip_path, extract_dir)
            
            submission_output_dir = os.path.join(output_dir, f"submission_{submission_id}")
            os.makedirs(submission_output_dir, exist_ok=True)
            
            for txlf_file in txlf_files:
                file_name = os.path.basename(txlf_file)
                output_file_path = os.path.join(submission_output_dir, file_name)
                
                success = await self.txlf_processor.process_txlf_file(
                    txlf_file,
                    output_file_path,
                    prompt_template,
                    rag_configs
                )
                
                if success:
                    results["files_processed"] += 1
                    results["output_files"].append(output_file_path)
                    
                    if self._check_file_passed(output_file_path):
                        results["files_passed"] += 1
            
            results["success"] = results["files_processed"] > 0
            return results
            
        except Exception as e:
            logger.error(f"Error processing submission {submission_id}: {e}")
            results["errors"].append(f"Error: {str(e)}")
            return results
