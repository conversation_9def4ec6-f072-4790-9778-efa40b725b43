"""
GLE (GlobalLink Enterprise) Service for translation quality evaluation integration.
"""
import asyncio
import json
import os
import tempfile
import time
import zipfile
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import xml.etree.ElementTree as ET

import aiohttp
import requests
from loguru import logger

from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig


class GLEService:
    """Service for integrating with GLE (GlobalLink Enterprise) system."""
    
    def __init__(self):
        self.base_url = "https://stg-paypal1.transperfect.com"
        self.auth_header = "Basic TUJnS3lLJiNEa3opKHprVjpiWE5FbkxlNkh0dXpQVzI5"
        self.username = "paypal_api_qe"
        self.password = "P4ypalAp1Q3!"
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None
        self.llm_evaluator = LLMTranslationEvaluator()
    
    async def authenticate(self) -> bool:
        """
        Authenticate with GLE system and get access token.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            url = f"{self.base_url}/PD/oauth/token"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": self.auth_header
            }
            data = {
                "grant_type": "password",
                "username": self.username,
                "password": self.password
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.access_token = result.get("access_token")
                        self.refresh_token = result.get("refresh_token")
                        
                        # Set token expiration time (default 20 minutes)
                        expires_in = result.get("expires_in", 1200)  # 20 minutes default
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)  # Refresh 1 minute early
                        
                        logger.info("Successfully authenticated with GLE system")
                        return True
                    else:
                        logger.error(f"Authentication failed with status {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False
    
    async def refresh_access_token(self) -> bool:
        """
        Refresh the access token using the refresh token.
        
        Returns:
            bool: True if refresh successful, False otherwise
        """
        if not self.refresh_token:
            logger.warning("No refresh token available, re-authenticating")
            return await self.authenticate()
        
        try:
            url = f"{self.base_url}/PD/oauth/token"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": self.auth_header
            }
            data = {
                "grant_type": "refresh_token",
                "refresh_token": self.refresh_token
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.access_token = result.get("access_token")
                        self.refresh_token = result.get("refresh_token")
                        
                        # Set token expiration time
                        expires_in = result.get("expires_in", 1200)
                        self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)
                        
                        logger.info("Successfully refreshed access token")
                        return True
                    else:
                        logger.error(f"Token refresh failed with status {response.status}")
                        return await self.authenticate()  # Fall back to full authentication
        except Exception as e:
            logger.error(f"Error during token refresh: {e}")
            return await self.authenticate()  # Fall back to full authentication
    
    async def ensure_valid_token(self) -> bool:
        """
        Ensure we have a valid access token.
        
        Returns:
            bool: True if we have a valid token, False otherwise
        """
        if not self.access_token:
            return await self.authenticate()
        
        if self.token_expires_at and datetime.now() >= self.token_expires_at:
            logger.info("Access token expired, refreshing...")
            return await self.refresh_access_token()
        
        return True
    
    async def get_claimable_submissions(self) -> List[Dict]:
        """
        Get claimable submissions for API_QE phase.
        
        Returns:
            List[Dict]: List of claimable submissions with LANGUAGE claim level
        """
        if not await self.ensure_valid_token():
            logger.error("Failed to get valid token")
            return []
        
        try:
            url = f"{self.base_url}/PD/rest/v0/submissions/claimable"
            params = {"phaseName": "API_QE"}
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        submissions = await response.json()
                        
                        # Filter for LANGUAGE claim level only
                        language_submissions = [
                            sub for sub in submissions 
                            if sub.get("claimLevel") == "LANGUAGE"
                        ]
                        
                        logger.info(f"Found {len(language_submissions)} claimable submissions with LANGUAGE claim level")
                        return language_submissions
                    else:
                        logger.error(f"Failed to get claimable submissions: {response.status}")
                        return []
        except Exception as e:
            logger.error(f"Error getting claimable submissions: {e}")
            return []
    
    async def claim_submissions(self, submissions: List[Dict]) -> bool:
        """
        Claim the specified submissions and languages.
        
        Args:
            submissions: List of submissions to claim
            
        Returns:
            bool: True if all claims successful, False otherwise
        """
        if not await self.ensure_valid_token():
            logger.error("Failed to get valid token")
            return False
        
        try:
            url = f"{self.base_url}/PD/rest/v0/submissions/claim"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            # Prepare claim requests
            claim_requests = []
            for submission in submissions:
                submission_id = submission.get("submissionId")
                languages = [lang.get("targetLanguageCode") for lang in submission.get("languages", [])]
                
                if submission_id and languages:
                    claim_requests.append({
                        "submissionId": submission_id,
                        "phaseName": "API_QE",
                        "languages": languages
                    })
            
            if not claim_requests:
                logger.warning("No valid claim requests to process")
                return True
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=claim_requests) as response:
                    if response.status == 200:
                        result = await response.json()
                        failed_items = result.get("failedItems")
                        
                        if failed_items:
                            logger.warning(f"Some items failed to claim: {failed_items}")
                            return False
                        else:
                            logger.info("Successfully claimed all submissions")
                            return True
                    else:
                        logger.error(f"Failed to claim submissions: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error claiming submissions: {e}")
            return False
    
    async def request_download(self, submission_id: int) -> Optional[str]:
        """
        Request a download for the specified submission.
        
        Args:
            submission_id: The submission ID to download
            
        Returns:
            Optional[str]: Download ID if successful, None otherwise
        """
        if not await self.ensure_valid_token():
            logger.error("Failed to get valid token")
            return None
        
        try:
            url = f"{self.base_url}/PD/rest/v0/submissions/{submission_id}/download"
            params = {
                "translatableFiles": "true",
                "phaseName": "API_QE"
            }
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        download_id = result.get("downloadId")
                        logger.info(f"Download requested for submission {submission_id}, download ID: {download_id}")
                        return download_id
                    else:
                        logger.error(f"Failed to request download for submission {submission_id}: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error requesting download for submission {submission_id}: {e}")
            return None
    
    async def check_download_status(self, submission_id: int, download_id: str) -> bool:
        """
        Check if the download is ready.
        
        Args:
            submission_id: The submission ID
            download_id: The download ID
            
        Returns:
            bool: True if download is ready, False otherwise
        """
        if not await self.ensure_valid_token():
            logger.error("Failed to get valid token")
            return False
        
        try:
            url = f"{self.base_url}/PD/rest/v0/submissions/{submission_id}/download"
            params = {"downloadId": download_id}
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        processing_finished = result.get("processingFinished", False)
                        logger.info(f"Download status for {download_id}: {'Ready' if processing_finished else 'Processing'}")
                        return processing_finished
                    else:
                        logger.error(f"Failed to check download status: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error checking download status: {e}")
            return False
    
    async def download_file(self, download_id: str, output_dir: str) -> Optional[str]:
        """
        Download the file using the download ID.
        
        Args:
            download_id: The download ID
            output_dir: Directory to save the downloaded file
            
        Returns:
            Optional[str]: Path to the downloaded file if successful, None otherwise
        """
        if not await self.ensure_valid_token():
            logger.error("Failed to get valid token")
            return None
        
        try:
            url = f"{self.base_url}/PD/rest/v0/submissions/download/{download_id}"
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        # Save the zip file
                        os.makedirs(output_dir, exist_ok=True)
                        zip_path = os.path.join(output_dir, f"{download_id}.zip")
                        
                        with open(zip_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)
                        
                        logger.info(f"Downloaded file to {zip_path}")
                        return zip_path
                    else:
                        logger.error(f"Failed to download file: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error downloading file: {e}")
            return None
    
    def extract_txlf_files(self, zip_path: str, extract_dir: str) -> List[str]:
        """
        Extract TXLF files from the downloaded zip.
        
        Args:
            zip_path: Path to the zip file
            extract_dir: Directory to extract files to
            
        Returns:
            List[str]: List of paths to extracted TXLF files
        """
        txlf_files = []
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
                
                # Find all TXLF files
                for root, dirs, files in os.walk(extract_dir):
                    for file in files:
                        if file.endswith('.txlf'):
                            txlf_path = os.path.join(root, file)
                            txlf_files.append(txlf_path)
                            logger.info(f"Found TXLF file: {txlf_path}")
            
            return txlf_files
        except Exception as e:
            logger.error(f"Error extracting TXLF files: {e}")
            return []
