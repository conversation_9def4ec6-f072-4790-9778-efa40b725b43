from fastapi import APIRouter, HTTPException, Query
from loguru import logger
from starlette import status

from app.rag.schemas import (
    IngestDataRequest,
    SearchRequest,
    SearchResponse,
    DropCollectionRequest,
    QueryCollectionResponse,
)
from app.rag.service import RAGService

rag_api_router = APIRouter(prefix="/ai-localization/rag")


@rag_api_router.post("/ingest-data")
async def ingest_data(ingest_data_request: IngestDataRequest):
    collection_name = ingest_data_request.collection_name
    data = ingest_data_request.data

    rag_service = RAGService()

    if not rag_service.collection_exists(collection_name):
        logger.info(f"Collection {collection_name} does not exist! Will create one.")
        rag_service.create_collection(collection_name)

    await rag_service.async_insert_data(collection_name, data)


@rag_api_router.get("/collections")
async def list_collections():
    rag_service = RAGService()
    return {"collections": rag_service.list_collections()}


@rag_api_router.get(
    "/collections/{collection_name}", response_model=QueryCollectionResponse
)
async def query_collection(
        collection_name: str,
        page_id: int = Query(default=0, description="Page number, starting from 0"),
        page_size: int = Query(default=10, description="Number of items per page"),
):
    rag_service = RAGService()

    if not rag_service.collection_exists(collection_name):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Collection {collection_name} not found.",
        )

    ret = rag_service.query_collection(collection_name, page_id, page_size)

    return ret


@rag_api_router.post("/drop-collection")
async def drop_collection(drop_collection_request: DropCollectionRequest):
    collection_name = drop_collection_request.collection_name
    rag_service = RAGService()

    if not rag_service.collection_exists(collection_name):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Collection {collection_name} not found.",
        )

    rag_service.drop_collection_name(collection_name=collection_name)


@rag_api_router.post("/search", response_model=SearchResponse)
async def search(search_request: SearchRequest):
    collection_name = search_request.collection_name
    texts = search_request.texts
    limit = search_request.limit

    rag_service = RAGService()

    if not rag_service.collection_exists(collection_name):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Collection {collection_name} not found.",
        )

    search_results = await rag_service.search(
        collection_name=collection_name, texts=texts, limit=limit
    )

    return {"search_results": search_results}
