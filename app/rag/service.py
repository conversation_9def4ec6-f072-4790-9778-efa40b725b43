import time

from google import genai
from google.genai import types
from google.genai.types import HttpOptions
from loguru import logger
from pymilvus import MilvusClient, DataType

from app.config import Config
from app.rag.schemas import IngestedData


class RAGService:
    def __init__(self):
        self.milvus_uri = Config().get("milvus", "uri")
        self.milvus_db = Config().get("milvus", "db")
        self.project_id = Config().get("google", "project_id")
        self.location = Config().get("google", "location")

    def collection_exists(self, collection_name: str):
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        collections = client.list_collections()
        return collection_name in collections

    def check_collection_schema(self, collection_name: str) -> dict:
        """Check if the collection schema matches expected configuration.
        
        Args:
            collection_name: Name of the collection to check
            
        Returns:
            dict: Schema validation results containing:
                - is_valid: bool indicating if schema is valid
                - errors: list of error messages if any
                - schema: actual schema of the collection
        """
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        
        # First check if collection exists
        if not self.collection_exists(collection_name):
            return {
                "is_valid": False,
                "errors": [f"Collection {collection_name} does not exist"],
                "schema": None
            }
            
        # Get collection schema
        schema = client.describe_collection(collection_name)
        
        errors = []
        
        # Expected fields and their types
        expected_fields = {
            "id": {"type": DataType.INT64, "is_primary": True},
            "text_for_embedding": {"type": DataType.VARCHAR, "max_length": 4096},
            "embedding": {"type": DataType.FLOAT_VECTOR, "dim": int(Config().get("google", "embedding_dim_qe"))},
            "text_for_retrieval": {"type": DataType.VARCHAR, "max_length": 4096}
        }
        
        # Check each expected field
        for field_name, expected_props in expected_fields.items():
            field = next((f for f in schema["fields"] if f["name"] == field_name), None)
            
            if not field:
                errors.append(f"Missing required field: {field_name}")
                continue
                
            # Check data type
            if field["type"] != expected_props["type"]:
                errors.append(
                    f"Field {field_name} has wrong type: expected {expected_props['type']}, "
                    f"got {field['type']}"
                )
            
            # Check dimension for vector field
            if field_name == "embedding":
                if field["params"].get("dim") != expected_props["dim"]:
                    errors.append(
                        f"Embedding dimension mismatch: expected {expected_props['dim']}, "
                        f"got {field['params'].get('dim')}"
                    )
            
            # Check max_length for varchar fields
            if field["type"] == DataType.VARCHAR:
                if field["params"].get("max_length") != expected_props["max_length"]:
                    errors.append(
                        f"Field {field_name} has wrong max_length: expected "
                        f"{expected_props['max_length']}, got {field['params'].get('max_length')}"
                    )
            
            # Check primary key
            if field_name == "id" and not field.get("is_primary"):
                errors.append("Field 'id' is not set as primary key")

        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "schema": schema
        }

    def create_collection(self, collection_name: str):
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        schema = MilvusClient.create_schema()

        schema.add_field(
            field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=True
        )
        schema.add_field(
            field_name="text_for_embedding",
            datatype=DataType.VARCHAR,
            max_length=4096,
        )
        schema.add_field(
            field_name="embedding",
            datatype=DataType.FLOAT_VECTOR,
            dim=int(Config().get("google", "embedding_dim_qe")),
        )
        schema.add_field(
            field_name="text_for_retrieval",
            datatype=DataType.VARCHAR,
            max_length=4096,
        )

        index_params = client.prepare_index_params()
        index_params.add_index(field_name="id", index_type="STL_SORT")
        index_params.add_index(
            field_name="embedding", index_type="AUTOINDEX", metric_type="COSINE"
        )
        client.create_collection(
            collection_name=collection_name,
            schema=schema,
            index_params=index_params,
        )
        logger.info(f"Collection {collection_name} created.")

    def drop_collection_name(self, collection_name: str):
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        client.drop_collection(collection_name=collection_name)
        print(f"Collection {collection_name} dropped!")

    def list_collections(self) -> list[str]:
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        collections = client.list_collections()
        return collections

    def query_collection(
        self, collection_name: str, page_id: int, page_size: int
    ) -> dict:
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)
        count_res = client.query(
            collection_name=collection_name,
            output_fields=["count(*)"],
        )
        total = count_res[0]["count(*)"]
        res = client.query(
            collection_name=collection_name,
            output_fields=["text_for_embedding", "text_for_retrieval"],
            offset=page_id * page_size,
            limit=page_size,
        )
        page_data = list(
            map(
                lambda x: {
                    "text_for_embedding": x["text_for_embedding"],
                    "text_for_retrieval": x["text_for_retrieval"],
                },
                res,
            )
        )
        return {
            "collection_name": collection_name,
            "data": page_data,
            "total": total,
            "page_id": page_id,
            "page_size": page_size,
        }

    def gemini_embedding(self, texts: list[str]) -> list[list[float]]:
        client = genai.Client(
            vertexai=True,
            project=self.project_id,
            location=self.location,
            http_options=HttpOptions(api_version="v1"),
        )

        embeddings = []
        for text in texts:
            result = client.models.embed_content(
                model=Config().get("google", "embedding_model_qe"),
                contents=text,
                config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT"),
            )
            embeddings.append(result.embeddings[0].values)

        return embeddings

    async def async_gemini_embedding(self, texts: list[str]) -> list[list[float]]:
        client = genai.Client(
            vertexai=True,
            project=self.project_id,
            location=self.location,
            http_options=HttpOptions(api_version="v1"),
        )

        embeddings = []
        for i, text in enumerate(texts):
            start = time.time()
            result = await client.aio.models.embed_content(
                model=Config().get("google", "embedding_model_qe"),
                contents=text,
                config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT"),
            )
            embeddings.append(result.embeddings[0].values)
            end = time.time()
            logger.info(f"Done embedding for {i}-th text: {text}, takes {end - start}s")

        return embeddings

    def insert_data(self, collection_name: str, data: list[IngestedData]):
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)

        texts_for_embedding = [item.text_for_embedding for item in data]
        embeddings = self.gemini_embedding(texts_for_embedding)

        insert_data = []
        for i, item in enumerate(data):
            insert_data.append(
                {
                    "text_for_embedding": item.text_for_embedding,
                    "embedding": embeddings[i],
                    "text_for_retrieval": item.text_for_retrieval,
                }
            )

        client.insert(collection_name=collection_name, data=insert_data)
        logger.info(
            f"Successfully inserted {len(data)} data into collection: {collection_name}"
        )

    async def async_insert_data(self, collection_name: str, data: list[IngestedData]):
        client = MilvusClient(uri=self.milvus_uri, db_name=self.milvus_db)

        texts_for_embedding = [item.text_for_embedding for item in data]
        embeddings = await self.async_gemini_embedding(texts_for_embedding)

        insert_data = []
        for i, item in enumerate(data):
            insert_data.append(
                {
                    "text_for_embedding": item.text_for_embedding,
                    "embedding": embeddings[i],
                    "text_for_retrieval": item.text_for_retrieval,
                }
            )

        client.insert(collection_name=collection_name, data=insert_data)
        logger.info(
            f"Successfully inserted {len(data)} data into collection: {collection_name}"
        )

    async def search(
        self, collection_name: str, texts: list[str], limit: int
    ) -> list[list[dict]]:
        embeddings = await self.async_gemini_embedding(texts)
        client = MilvusClient(
            uri="http://10.183.34.136:19530", db_name="ai_localization"
        )
        response = client.search(
            collection_name=collection_name,
            anns_field="embedding",
            data=embeddings,
            limit=limit,
            search_params={"metric_type": "COSINE"},
            output_fields=["text_for_embedding", "text_for_retrieval"],
        )
        search_results = []
        for hits in response:
            search_results.append(list(map(lambda x: x["entity"], hits)))

        return search_results
