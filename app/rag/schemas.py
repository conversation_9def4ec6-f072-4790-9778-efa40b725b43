from pydantic import BaseModel


class IngestedData(BaseModel):
    text_for_embedding: str
    text_for_retrieval: str


class IngestDataRequest(BaseModel):
    collection_name: str
    data: list[IngestedData]


class QueryCollectionResponse(BaseModel):
    collection_name: str
    data: list[IngestedData]
    total: int
    page_id: int
    page_size: int


class SearchRequest(BaseModel):
    collection_name: str
    texts: list[str]
    limit: int


class SearchResponse(BaseModel):
    search_results: list[list[IngestedData]]


class DropCollectionRequest(BaseModel):
    collection_name: str
