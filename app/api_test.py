import base64
import json

import requests

from app.llm_translation_qe.schemas import LLMConfiguration

# from app.llm_translation_qe.schemas import LLMConfiguration

base_url = "http://*************:8082"


# base_url = "https://aiplatform.dev51.cbf.dev.paypalinc.com/byoa/jupiter-dev-gcp-3300c/jupiter-dev-gcp-vm"


def test_upload_files():
    url = f"{base_url}/llm-translation-qe/bilingual-files-evaluation"
    files = [
        ("files", open("a.txt", "rb")),
        ("files", open("b.txt", "rb")),
    ]
    # data = {"name": "foo", "point": 0.13, "is_accepted": False}
    dummy_request_data = {
        "evaluation_template_json": json.dumps(
            {
                "source_language": "English",
                "target_language": "Chinese",
                "evaluation_criteria": "Evaluate the translation quality.",
                "evaluation_result_fields": [
                    {
                        "name": "score",
                        "description": "The score of the translation quality (0-100)",
                    },
                ],
                "few_shot_examples": [
                    {
                        "source_text": "Hello world",
                        "target_text": "你好世界",
                        "score": "100",
                    }
                ],
            }
        )
    }
    resp = requests.post(url=url, data=dummy_request_data, files=files)
    # resp = requests.post(url=url, data=json.dumps(dummy_request_data))
    resp.raise_for_status()
    print(resp.json())


def test_call_llm_api():
    url = f"{base_url}/llm-translation-qe/call-llm"
    request = {
        "prompt_template": {
            "template_messages": [
                {
                    "role": "system",
                    "content": "Your name is {{name}}.",
                },
                {
                    "role": "user",
                    "content": "What is your name?",
                },
            ],
            "template_variables": [
                {"key": "name", "value": "Charles"},
            ],
            "llm_configuration": {
                "model_name": "google/gemini-1.5-flash-002",
                "temperature": 0.1,
                "top_p": 0.1,
            },
            "output_schema": [],
            "selected_glossary": None,
        }
    }

    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.json())


def test_create_prompt_api():
    url = f"{base_url}/prompts-library/prompts/create"
    headers = {"Content-Type": "application/json"}
    request = {
        "name": "English-Cantonese Translation Prompt",
        "description": "The prompt for translating English into Cantonese",
        "created_by": "jianpeng",
    }
    response = requests.post(url, headers=headers, json=request)
    print(response.text)


def test_update_prompt_api():
    url = f"{base_url}/prompts-library/prompts/update"
    headers = {"Content-Type": "application/json"}
    request = {
        "prompt_id": "b45847c9-ad65-4091-9321-24710233febb",
        "name": "English to Chinese Translation Prompt",
        "description": "The prompt for translating English into Chinese",
    }
    response = requests.post(url, headers=headers, json=request)
    print(response.text)


def test_update_prompt_version_api():
    url = f"{base_url}/prompts-library/prompts/versions/update"
    headers = {"Content-Type": "application/json"}
    request = {
        "version_id": "96993f3c-21dd-41a7-a89e",
        "template_messages": [{"role": "user", "content": "Who are you?"}],
        "llm_configuration": LLMConfiguration().model_dump(),
        "output_schema": [],
    }
    response = requests.post(url, headers=headers, json=request)
    print(response.text)


def test_delete_prompt():
    url = f"{base_url}/prompts-library/prompts/delete"
    headers = {"Content-Type": "application/json"}
    request = {
        "prompt_id": "ba11fc64-2689-47f7-bc67-ac9b5bfacf5f",
    }
    response = requests.post(url, headers=headers, json=request)
    print(response.text)


def debug_update_prompt_version():
    request = {
        "version_id": "79ac8c58-334e-4d4e-be13-8119cdd0b0a6",
        "template_messages": [{"id": 0, "role": "system", "content": "你好"}],
        "llm_configuration": {
            "model_name": "google/gemini-2.0-flash-001",
            "temperature": 0.05,
            "top_p": 0.5,
        },
        "output_schema": [],
    }
    url = (
        f"https://aiplatform.dev51.cbf.dev.paypalinc.com/byoa/jupiter-dev-gcp-3300c/ai-localization-dev/prompts"
        f"-library/prompts/versions/update"
    )
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, headers=headers, json=request)
    print(response.text)


def test_image_understanding():
    def encode_image(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    image_base64_data = encode_image("gnx.png")

    url = f"{base_url}/llm-translation-qe/call-llm"
    request = {
        "prompt_template": {
            "template_messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Who is the man in the image?",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64_data}"
                            },
                        },
                    ],
                },
            ],
            "template_variables": [
                {"key": "name", "value": "Kanye West"},
            ],
            "llm_configuration": {
                "model_name": "google/gemini-2.0-flash",
                "temperature": 0.1,
                "top_p": 0.1,
            },
            "output_schema": [
                {
                    "name": "reason",
                    "type": "string",
                    "description": "The reason for your answer",
                    "required": True,
                },
                {
                    "name": "answer",
                    "type": "string",
                    "description": "Your answer",
                    "required": True,
                },
            ],
        }
    }

    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.json())


def test_call_llm():
    url = f"{base_url}/llm-translation-qe/call-llm"
    request = {
        "prompt_template": {
            "template_messages": [
                {
                    "role": "user",
                    "content": """\
Answer below questions:
1. What is the full name of Charles?
2. What does Charles do?
3. In what year was Charles born?

You can answer based on below context:
{{about_charles_1}}
---
{{about_charles_2}}""",
                },
            ],
            "template_variables": [],
            "llm_configuration": {
                "model_name": "google/gemini-2.5-pro-preview-03-25",
                "temperature": 0.1,
                "top_p": 0.1,
            },
            # "json_schema": {
            #     "type": "object",
            #     "properties": {
            #         "reason": {
            #             "type": "string",
            #             "description": "The reason for the answer",
            #         },
            #         "answer": {
            #             "type": "string",
            #             "description": "The answer",
            #         },
            #     },
            #     "required": ["reason", "answer"],
            # },
        },
        "rag_configs": [
            {
                "collection_name": "about_charles",
                "texts": ["What is the full name of Charles?", "What does Charles do?"],
                "limit": 1,
                "placeholder_key": "about_charles_1",
            },
            {
                "collection_name": "about_charles",
                "texts": ["In what year was Charles born?"],
                "limit": 1,
                "placeholder_key": "about_charles_2",
            },
        ],
    }

    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.json())


def test_document_understanding():
    def encode_file(file_path):
        with open(file_path, "rb") as file:
            return base64.b64encode(file.read()).decode("utf-8")

    file_base64_data = encode_file("mlops-on-GCP-test.pdf")

    url = f"{base_url}/llm-translation-qe/call-llm"
    request = {
        "prompt_template": {
            "template_messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Summarize this document please {{rag_response}}",
                        },
                        {
                            "type": "file",
                            "file": {
                                "file_data": f"data:application/pdf;base64,{file_base64_data}"
                            },
                        },
                    ],
                },
            ],
            "llm_configuration": {
                "model_name": "google/gemini-2.0-flash",
                "temperature": 0.1,
                "top_p": 0.1,
            },
            "output_schema": [
                {
                    "name": "reason",
                    "type": "string",
                    "description": "The reason for your answer",
                    "required": True,
                },
                {
                    "name": "answer",
                    "type": "string",
                    "description": "Your answer",
                    "required": True,
                },
            ],
            "rag_config": {
                "query": ["source text"],
                "vector_db_table": "",
                "result_num": 3,
            },
        }
    }

    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.text)
    # print(response.json())


def test_rag_ingest_data():
    data = [
        {
            "text_for_embedding": "Full name of Charles",
            "text_for_retrieval": "The full name of Charles is Charles Peng.",
        },
        {
            "text_for_embedding": "The birth year of Charles",
            "text_for_retrieval": "The birth year of Charles is 1998.",
        },
        {
            "text_for_embedding": "What does Charles do for a living?",
            "text_for_retrieval": "Charles is a software engineer.",
        },
    ]
    collection_name = "about_charles"
    url = f"{base_url}/ai-localization/rag/ingest-data"
    request = {"collection_name": collection_name, "data": data}
    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.text)


def test_rag_search():
    collection_name = "test_collection_1"
    texts = ["Hello World", "Who am I?"]
    limit = 2
    url = f"{base_url}/ai-localization/rag/search"
    request = {"collection_name": collection_name, "texts": texts, "limit": limit}
    response = requests.post(
        url, headers={"Content-Type": "application/json"}, json=request
    )
    print(response.text)


def test_list_collections():
    url = f"{base_url}/ai-localization/rag/collections"
    response = requests.get(url)
    print(response.text)


def test_query_collection():
    url = f"{base_url}/ai-localization/rag/collections/about_charles?page_id=0&page_size=10"
    response = requests.get(url)
    print(json.dumps(response.json(), indent=4))


def test_drop_all_collections():
    url = f"{base_url}/ai-localization/rag/collections"
    response = requests.get(url)
    collections = response.json()["collections"]

    for collection_name in collections:
        url = f"{base_url}/ai-localization/rag/drop-collection"
        request = {"collection_name": collection_name}
        response = requests.post(
            url=url, json=request, headers={"Content-Type": "application/json"}
        )
        print(response.status_code)


if __name__ == "__main__":
    # test_query_collection()
    test_list_collections()
    # test_drop_all_collections()
