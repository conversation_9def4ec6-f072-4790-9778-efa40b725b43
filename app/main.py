import uvicorn
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware.cors import CORSMiddleware

from app.llm_translation_qe.api import llm_translation_qe_api_router
from app.prompts_library.api import prompts_library_api_router
from app.rag.api import rag_api_router
from app.gle.api import gle_api_router

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,  # Specify if credentials are allowed
    allow_methods=["*"],  # Specify allowed HTTP methods
    allow_headers=["*"],  # Specify allowed HTTP headers
)
app.include_router(llm_translation_qe_api_router)
app.include_router(prompts_library_api_router)
app.include_router(rag_api_router)
app.include_router(gle_api_router)

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        reload=False,
        workers=1,
    )
