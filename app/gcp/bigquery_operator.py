import uuid
from datetime import timezone, datetime

from google.cloud import bigquery


class BigQueryOperator:
    def __init__(self, project: str, location: str):
        self._client = bigquery.Client(project=project, location=location)

    @property
    def client(self):
        return self._client

    # TODO: Add necessary methods for BQ operation


def insert_dummy_prompts():
    project_id = "dev51-test-apps-jupiter-rsh"
    location = "us-central1"
    bq_operator = BigQueryOperator(project=project_id, location=location)
    bq_client = bq_operator.client
    current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")
    print(f"Current time: {current_time}")

    dummy_prompt_1 = {
        "prompt_id": str(uuid.uuid4()),
        "name": "English_Chinese Translation Prompt",
        "description": "This is the prompt for translating English into Chinese",
        "owner": "jianpeng",
        "created_at": current_time,
        "updated_at": current_time,
    }

    dummy_prompt_2 = {
        "prompt_id": str(uuid.uuid4()),
        "name": "English_Chinese QE Prompt",
        "description": "The prompt for evaluating translation between English and Chinese",
        "owner": "jianpeng",
        "created_at": current_time,
        "updated_at": current_time,
    }

    rows_to_insert = [dummy_prompt_1, dummy_prompt_2]
    bq_client.insert_rows_json(
        table="llm_translation_qe.prompts", json_rows=rows_to_insert
    )


def test_insert_dummy_prompt_version():
    project_id = "dev51-test-apps-jupiter-rsh"
    location = "us-central1"
    bq_operator = BigQueryOperator(project=project_id, location=location)
    bq_client = bq_operator.client
    current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")
    print(f"Current time: {current_time}")

    dummy_prompt_version_1 = {
        "version_id": str(uuid.uuid4()),
        "prompt_id": "b45847c9-ad65-4091-9321-24710233febb",
        "version_number": 1,
        "created_by": "jianpeng",
        "created_at": current_time,
        "updated_at": current_time,
        "template_messages": [
            {
                "role": "system",
                "content": "You are a translator between English and Chinese.",
            },
            {
                "role": "user",
                "content": "Hello, world!",
            },
        ],
        "llm_configuration": {
            "model_name": "google/gemini-2.0-flash-001",
            "temperature": 0.0,
            "top_p": 0.1,
        },
        "output_schema": [
            {
                "name": "translated_text",
                "type": "string",
                "description": "The text after translation",
                "required": True,
            }
        ],
        "selected_glossary": None,
    }

    rows_to_insert = [dummy_prompt_version_1]
    bq_client.insert_rows_json(
        table="llm_translation_qe.prompt_versions", json_rows=rows_to_insert
    )


def test_list_rows_from_prompts():
    project_id = "dev51-test-apps-jupiter-rsh"
    location = "us-central1"
    dataset = "ai_localization_dev"

    sql = f"SELECT * FROM {project_id}.{dataset}.prompts;"

    bq_client = BigQueryOperator(project=project_id, location=location).client
    rows_iter = bq_client.query_and_wait(sql)
    rows = list(rows_iter)
    print(rows)


if __name__ == "__main__":
    # insert_dummy_prompts()
    # test_insert_dummy_prompt_version()
    test_list_rows_from_prompts()
