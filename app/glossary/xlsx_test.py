import json

import pandas as pd
from google import genai
from google.genai import types
from google.genai.types import HttpOptions


def read_xlsx() -> list[dict]:
    df = pd.read_excel("app/glossary/english-chinese-glossary.xlsx", engine="openpyxl")
    glossary = []
    for _, row in df.iterrows():
        row = row.where(pd.notna(row), None).to_dict()
        english_terms = []
        chinese_terms = []
        for column, value in row.items():
            if "en" in column and ":" not in column and value is not None:
                english_terms.append(value)
            if "zh" in column and ":" not in column and value is not None:
                chinese_terms.append(value)
        glossary.append({"en": english_terms, "zh": chinese_terms})

    return glossary[:10]


def embedding(glossary: list[dict]) -> list[dict]:
    glossary_with_embd = []
    for term in glossary:
        english_terms_list = term["en"]
        chinese_terms_list = term["zh"]

        if len(english_terms_list) == 0 or len(chinese_terms_list) == 0:
            continue

        english_terms = ", ".join(english_terms_list)
        english_terms_embedding = gemini_embedding(english_terms)

        glossary_with_embd.append(term | {"embedding": english_terms_embedding})

    return glossary_with_embd


def gemini_embedding(text: str) -> list[float]:
    print(f"Generating embedding for {text}")
    project_id = "dev51-test-apps-jupiter-rsh"
    location = "us-central1"
    client = genai.Client(
        vertexai=True,
        project=project_id,
        location=location,
        http_options=HttpOptions(api_version="v1"),
    )
    result = client.models.embed_content(
        model="text-embedding-004",
        contents=text,
        config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT"),
    )
    return list(map(lambda embedding: embedding.values, result.embeddings))


if __name__ == "__main__":
    glossary = read_xlsx()
    glossary_with_embedding = embedding(glossary)
    print(json.dumps(glossary_with_embedding, indent=4, ensure_ascii=False))
