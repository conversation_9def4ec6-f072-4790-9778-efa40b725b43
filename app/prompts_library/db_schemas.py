from sqlalchemy import Column, String, DateTime, func, Text, ForeignKey, Integer, JSON
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class Prompt(Base):
    __tablename__ = "prompts"

    prompt_id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text)
    created_by = Column(String)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    versions = relationship(
        "PromptVersion", back_populates="prompt", cascade="all, delete-orphan"
    )


class PromptVersion(Base):
    __tablename__ = "prompt_versions"

    version_id = Column(String, primary_key=True, index=True)
    prompt_id = Column(
        String, ForeignKey("prompts.prompt_id", ondelete="CASCADE"), nullable=False
    )
    version_number = Column(Integer, nullable=False)
    created_by = Column(String, nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(
        DateTime, nullable=False, default=func.now(), onupdate=func.now()
    )
    template_messages = Column(JSON, nullable=False)
    llm_configuration = Column(JSON, nullable=False)
    output_schema = Column(JSON, nullable=False)

    prompt = relationship("Prompt", back_populates="versions")
