from pydantic import BaseModel

from app.llm_translation_qe.schemas import (
    TemplateMessage,
    LLMConfiguration,
    OutputSchemaItem,
)


class Prompt(BaseModel):
    prompt_id: str
    name: str
    description: str
    created_by: str
    created_at: str
    updated_at: str


class PromptVersionInfo(BaseModel):
    version_id: str
    version_number: int
    created_by: str
    created_at: str


class PromptWithVersions(BaseModel):
    prompt_id: str
    name: str
    description: str
    created_by: str
    created_at: str
    updated_at: str
    versions: list[PromptVersionInfo] = []


class PromptVersion(BaseModel):
    version_id: str
    prompt_id: str
    version_number: int
    created_by: str
    created_at: str
    updated_at: str
    template_messages: list[TemplateMessage]
    llm_configuration: LLMConfiguration
    output_schema: list[OutputSchemaItem]


class CreatePromptRequest(BaseModel):
    name: str
    description: str
    created_by: str


class CreatePromptResponse(BaseModel):
    prompt_id: str


class CreatePromptVersionRequest(BaseModel):
    prompt_id: str
    created_by: str
    template_messages: list[TemplateMessage]
    llm_configuration: LLMConfiguration
    output_schema: list[OutputSchemaItem]


class CreatePromptVersionResponse(BaseModel):
    version_id: str


class UpdatePromptRequest(BaseModel):
    prompt_id: str
    name: str
    description: str


class UpdatePromptVersionRequest(BaseModel):
    version_id: str
    template_messages: list[TemplateMessage]
    llm_configuration: LLMConfiguration
    output_schema: list[OutputSchemaItem]


class DeletePromptRequest(BaseModel):
    prompt_id: str


class DeletePromptVersionRequest(BaseModel):
    version_id: str
