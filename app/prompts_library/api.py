from fastapi import APIRouter, Query, HTTPException
from loguru import logger
from sqlalchemy.exc import NoResultFound
from starlette import status

from app.prompts_library.schemas import (
    Prompt,
    PromptWithVersions,
    PromptVersionInfo,
    CreatePromptResponse,
    CreatePromptRequest,
    UpdatePromptRequest,
    DeletePromptRequest,
    PromptVersion,
    CreatePromptVersionResponse,
    CreatePromptVersionRequest,
    UpdatePromptVersionRequest,
    DeletePromptVersionRequest,
)
from app.prompts_library.service import PromptsLibraryService

prompts_library_api_router = APIRouter(prefix="/prompts-library")


@prompts_library_api_router.get("/prompts", response_model=list[Prompt])
def get_prompts_page(
    page_id: int = Query(default=0, description="Page number, starting from 0"),
    page_size: int = Query(default=10, description="Number of items per page"),
):
    service = PromptsLibraryService()
    try:
        prompts = service.get_prompts_page(page_id=page_id, page_size=page_size)
        ret = [
            Prompt(
                prompt_id=prompt.prompt_id,
                name=prompt.name,
                description=prompt.description,
                created_by=prompt.created_by,
                created_at=prompt.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                updated_at=prompt.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            )
            for prompt in prompts
        ]
        return ret
    except Exception as e:
        logger.error(
            f"Failed to get prompts page_id {page_id}, page_size {page_size}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.get("/prompts/all", response_model=list[PromptWithVersions])
def get_all_prompts():
    """Get all prompts with their version information."""
    service = PromptsLibraryService()
    try:
        prompts_with_versions = service.get_all_prompts_with_versions()
        ret = []

        for prompt_dict in prompts_with_versions:
            # Format datetime strings
            created_at = prompt_dict["created_at"].strftime("%Y-%m-%d %H:%M:%S.%f") if hasattr(prompt_dict["created_at"], "strftime") else prompt_dict["created_at"]
            updated_at = prompt_dict["updated_at"].strftime("%Y-%m-%d %H:%M:%S.%f") if hasattr(prompt_dict["updated_at"], "strftime") else prompt_dict["updated_at"]

            # Format version datetime strings
            versions = []
            for version in prompt_dict["versions"]:
                version_created_at = version["created_at"].strftime("%Y-%m-%d %H:%M:%S.%f") if hasattr(version["created_at"], "strftime") else version["created_at"]

                versions.append(
                    PromptVersionInfo(
                        version_id=version["version_id"],
                        version_number=version["version_number"],
                        created_by=version["created_by"],
                        created_at=version_created_at
                    )
                )

            # Create PromptWithVersions object
            prompt_with_versions = PromptWithVersions(
                prompt_id=prompt_dict["prompt_id"],
                name=prompt_dict["name"],
                description=prompt_dict["description"],
                created_by=prompt_dict["created_by"],
                created_at=created_at,
                updated_at=updated_at,
                versions=versions
            )

            ret.append(prompt_with_versions)

        return ret
    except Exception as e:
        logger.error(f"Failed to get all prompts with versions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.get("/prompts/name/{prompt_name}", response_model=Prompt)
def get_prompt_by_name(prompt_name: str):
    """Get a prompt by its name."""
    service = PromptsLibraryService()
    prompt = service.get_prompt_by_name(prompt_name=prompt_name)

    if prompt is not None:
        return Prompt(
            prompt_id=prompt.prompt_id,
            name=prompt.name,
            description=prompt.description,
            created_by=prompt.created_by,
            created_at=prompt.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            updated_at=prompt.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
        )
    else:
        logger.error(f"Prompt with name '{prompt_name}' not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt with name '{prompt_name}' not found.",
        )


@prompts_library_api_router.get(
    "/prompts/name/{prompt_name}/versions/{version_number}", response_model=PromptVersion
)
def get_prompt_version_by_name_and_number(prompt_name: str, version_number: int):
    """Get a specific prompt version by prompt name and version number."""
    service = PromptsLibraryService()
    prompt_version = service.get_prompt_version_by_name_and_number(
        prompt_name=prompt_name, version_number=version_number
    )

    if prompt_version is not None:
        return PromptVersion(
            version_id=prompt_version.version_id,
            prompt_id=prompt_version.prompt_id,
            version_number=prompt_version.version_number,
            created_by=prompt_version.created_by,
            created_at=prompt_version.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            updated_at=prompt_version.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            template_messages=prompt_version.template_messages,
            llm_configuration=prompt_version.llm_configuration,
            output_schema=prompt_version.output_schema,
        )
    else:
        logger.error(
            f"Prompt version {version_number} for prompt '{prompt_name}' not found."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt version {version_number} for prompt '{prompt_name}' not found.",
        )


@prompts_library_api_router.get("/prompts/{prompt_id}", response_model=Prompt)
def get_prompt(prompt_id: str):
    service = PromptsLibraryService()
    prompt = service.get_prompt(prompt_id=prompt_id)

    if prompt is not None:
        return Prompt(
            prompt_id=prompt.prompt_id,
            name=prompt.name,
            description=prompt.description,
            created_by=prompt.created_by,
            created_at=prompt.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
            updated_at=prompt.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
        )
    else:
        logger.error(f"Prompt {prompt_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt {prompt_id} not found.",
        )


@prompts_library_api_router.post("/prompts/create", response_model=CreatePromptResponse)
def create_prompt(request: CreatePromptRequest):
    prompt_name = request.name
    prompt_description = request.description
    created_by = request.created_by

    service = PromptsLibraryService()

    try:
        new_prompt_id = service.create_prompt(
            prompt_name=prompt_name,
            description=prompt_description,
            created_by=created_by,
        )
        logger.info(f"Created prompt {new_prompt_id}.")
        return CreatePromptResponse(prompt_id=new_prompt_id)
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.post("/prompts/update")
def update_prompt(request: UpdatePromptRequest):
    service = PromptsLibraryService()

    prompt_id = request.prompt_id
    prompt_name = request.name
    prompt_description = request.description

    try:
        service.update_prompt(
            prompt_id=prompt_id,
            prompt_name=prompt_name,
            prompt_description=prompt_description,
        )
        logger.info(f"Updated prompt {prompt_id}.")
    except NoResultFound:
        logger.error(f"Prompt {prompt_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt {prompt_id} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.post("/prompts/delete")
def delete_prompt(request: DeletePromptRequest):
    service = PromptsLibraryService()

    prompt_id = request.prompt_id

    try:
        service.delete_prompt(prompt_id=prompt_id)
        logger.info(f"Deleted prompt {prompt_id}")
    except NoResultFound:
        logger.error(f"Prompt {prompt_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt {prompt_id} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.get(
    "/prompts/{prompt_id}/versions", response_model=list[PromptVersion]
)
def get_prompt_versions(prompt_id: str):
    service = PromptsLibraryService()
    try:
        prompt_versions = service.get_prompt_versions(prompt_id=prompt_id)
        return [
            PromptVersion(
                version_id=prompt_version.version_id,
                prompt_id=prompt_version.prompt_id,
                version_number=prompt_version.version_number,
                created_by=prompt_version.created_by,
                created_at=prompt_version.created_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                updated_at=prompt_version.updated_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
                template_messages=prompt_version.template_messages,
                llm_configuration=prompt_version.llm_configuration,
                output_schema=prompt_version.output_schema,
            )
            for prompt_version in prompt_versions
        ]
    except NoResultFound:
        logger.error(f"Prompt {prompt_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt {prompt_id} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.post(
    "/prompts/versions/create", response_model=CreatePromptVersionResponse
)
def create_prompt_version(request: CreatePromptVersionRequest):
    service = PromptsLibraryService()

    request_dict = request.model_dump()
    try:
        new_prompt_version_id = service.create_prompt_version(
            prompt_id=request_dict["prompt_id"],
            created_by=request_dict["created_by"],
            template_messages=request_dict["template_messages"],
            llm_configuration=request_dict["llm_configuration"],
            output_schema=request_dict["output_schema"],
        )
        logger.info(f"Created prompt version {new_prompt_version_id}.")
        return CreatePromptVersionResponse(version_id=new_prompt_version_id)
    except NoResultFound:
        logger.error(f"Prompt {request_dict['prompt_id']} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt {request_dict['prompt_id']} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.post("/prompts/versions/update")
def update_prompt_version(request: UpdatePromptVersionRequest):
    service = PromptsLibraryService()

    request_dict = request.model_dump()
    try:
        service.update_prompt_version(
            version_id=request_dict["version_id"],
            template_messages=request_dict["template_messages"],
            llm_configuration=request_dict["llm_configuration"],
            output_schema=request_dict["output_schema"],
        )
        logger.info(f"Updated prompt version: {request_dict['version_id']}.")
    except NoResultFound:
        logger.error(f"Prompt version {request_dict['version_id']} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt version {request_dict['version_id']} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@prompts_library_api_router.post("/prompts/versions/delete")
def delete_prompt_version(request: DeletePromptVersionRequest):
    service = PromptsLibraryService()

    version_id = request.version_id
    try:
        service.delete_prompt_version(version_id=version_id)
        logger.info(f"Deleted prompt version {version_id}")
    except NoResultFound:
        logger.error(f"Prompt version {version_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prompt version {version_id} not found.",
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
