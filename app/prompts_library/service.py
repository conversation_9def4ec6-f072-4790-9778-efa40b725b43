import uuid
from datetime import timezone, datetime
from typing import Type, Optional

from sqlalchemy import create_engine, desc
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import sessionmaker

from app.prompts_library.db_schemas import Prompt, PromptVersion


class PromptsLibraryService:
    def __init__(self):
        ssl_root_cert = "app/postgres_ssl_cert/server-ca.pem"
        ssl_cert = "app/postgres_ssl_cert/client-cert.pem"
        ssl_key = "app/postgres_ssl_cert/client-key.pem"
        ssl_args = {
            "sslmode": "verify-ca",
            "sslcert": ssl_cert,
            "sslkey": ssl_key,
            "sslrootcert": ssl_root_cert,
        }
        database_url = (
            "postgresql+psycopg2://postgres:postgres@10.183.153.65/ai_localization"
        )
        self.engine = create_engine(database_url, connect_args=ssl_args)
        self.session_local = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )

    def get_prompts_page(self, page_id: int, page_size: int) -> list[Type[Prompt]]:
        db = self.session_local()
        try:
            offset = page_id * page_size
            prompts = (
                db.query(Prompt)
                .order_by(desc(Prompt.created_at))
                .offset(offset)
                .limit(page_size)
                .all()
            )
            return prompts
        finally:
            db.close()

    def get_all_prompts(self) -> list[Type[Prompt]]:
        """Get all prompts without pagination."""
        db = self.session_local()
        try:
            prompts = (
                db.query(Prompt)
                .order_by(desc(Prompt.created_at))
                .all()
            )
            return prompts
        finally:
            db.close()

    def get_all_prompts_with_versions(self) -> list[dict]:
        """Get all prompts with their version information."""
        db = self.session_local()
        try:
            prompts = (
                db.query(Prompt)
                .order_by(desc(Prompt.created_at))
                .all()
            )

            result = []
            for prompt in prompts:
                # Get all versions for this prompt
                versions = (
                    db.query(PromptVersion)
                    .filter(PromptVersion.prompt_id == prompt.prompt_id)
                    .order_by(desc(PromptVersion.version_number))
                    .all()
                )

                # Create a dictionary with prompt and version information
                prompt_dict = {
                    "prompt_id": prompt.prompt_id,
                    "name": prompt.name,
                    "description": prompt.description,
                    "created_by": prompt.created_by,
                    "created_at": prompt.created_at,
                    "updated_at": prompt.updated_at,
                    "versions": [
                        {
                            "version_id": version.version_id,
                            "version_number": version.version_number,
                            "created_by": version.created_by,
                            "created_at": version.created_at,
                        }
                        for version in versions
                    ]
                }

                result.append(prompt_dict)

            return result
        finally:
            db.close()

    def get_prompt_by_name(self, prompt_name: str) -> Optional[Type[Prompt]]:
        """Get a prompt by its name."""
        db = self.session_local()
        try:
            prompt = (
                db.query(Prompt)
                .filter(Prompt.name == prompt_name)
                .first()
            )
            return prompt
        finally:
            db.close()

    def get_prompt_version_by_name_and_number(self, prompt_name: str, version_number: int) -> Optional[Type[PromptVersion]]:
        """Get a specific prompt version by prompt name and version number."""
        db = self.session_local()
        try:
            # First get the prompt by name
            prompt = (
                db.query(Prompt)
                .filter(Prompt.name == prompt_name)
                .first()
            )

            if not prompt:
                return None

            # Then get the specific version
            prompt_version = (
                db.query(PromptVersion)
                .filter(
                    PromptVersion.prompt_id == prompt.prompt_id,
                    PromptVersion.version_number == version_number
                )
                .first()
            )

            return prompt_version
        finally:
            db.close()

    def get_prompt(self, prompt_id) -> Optional[Type[Prompt]]:
        db = self.session_local()
        try:
            prompt = db.get_one(Prompt, prompt_id)
            return prompt
        except NoResultFound:
            return None
        finally:
            db.close()

    def create_prompt(self, prompt_name: str, description: str, created_by: str) -> str:
        db = self.session_local()
        try:
            current_time = datetime.now(timezone.utc)
            new_prompt = Prompt(
                prompt_id=str(uuid.uuid4()),
                name=prompt_name,
                description=description,
                created_by=created_by,
                created_at=current_time,
                updated_at=current_time,
            )
            db.add(new_prompt)
            db.commit()
            return new_prompt.prompt_id
        finally:
            db.close()

    def update_prompt(self, prompt_id: str, prompt_name: str, prompt_description: str):
        db = self.session_local()
        try:
            current_time = datetime.now(timezone.utc)
            prompt = db.get_one(Prompt, prompt_id)
            prompt.name = prompt_name
            prompt.description = prompt_description
            prompt.updated_at = current_time
            db.commit()
        finally:
            db.close()

    def delete_prompt(self, prompt_id: str):
        db = self.session_local()
        try:
            prompt = db.get_one(Prompt, prompt_id)
            db.delete(prompt)
            db.commit()
        finally:
            db.close()

    def get_prompt_versions(self, prompt_id: str) -> list[Type[PromptVersion]]:
        db = self.session_local()
        try:
            prompt = db.get_one(Prompt, prompt_id)
            prompt_versions = prompt.versions
            prompt_versions.sort(key=lambda x: x.version_number, reverse=True)
            return prompt_versions
        finally:
            db.close()

    def create_prompt_version(
            self,
            prompt_id: str,
            created_by: str,
            template_messages: list,
            llm_configuration: dict,
            output_schema: list,
    ) -> str:
        db = self.session_local()
        try:
            current_time = datetime.now(timezone.utc)
            prompt = db.get_one(Prompt, prompt_id)

            # Get current max version number
            if prompt.versions:
                max_version_number = max(
                    version.version_number for version in prompt.versions
                )
            else:
                max_version_number = 0

            new_version_number = max_version_number + 1
            new_prompt_version = PromptVersion(
                version_id=str(uuid.uuid4()),
                prompt_id=prompt_id,
                version_number=new_version_number,
                created_by=created_by,
                created_at=current_time,
                updated_at=current_time,
                template_messages=template_messages,
                llm_configuration=llm_configuration,
                output_schema=output_schema,
            )
            db.add(new_prompt_version)
            db.commit()
            return new_prompt_version.version_id
        finally:
            db.close()

    def update_prompt_version(
            self,
            version_id: str,
            template_messages: list,
            llm_configuration: dict,
            output_schema: list,
    ):
        db = self.session_local()
        try:
            current_time = datetime.now(timezone.utc)
            prompt_version = db.get_one(PromptVersion, version_id)
            prompt_version.template_messages = template_messages
            prompt_version.llm_configuration = llm_configuration
            prompt_version.output_schema = output_schema
            prompt_version.updated_at = current_time
            db.commit()
        finally:
            db.close()

    def delete_prompt_version(self, version_id: str):
        db = self.session_local()
        try:
            prompt_version = db.get_one(PromptVersion, version_id)
            db.delete(prompt_version)
            db.commit()
        finally:
            db.close()
