[google]
project_id = dev51-test-apps-jupiter-rsh
location = us-central1
dataset = ai_localization_dev
embedding_model = text-embedding-large-exp-03-07
embedding_dim = 3072
embedding_model_qe = text-multilingual-embedding-002
embedding_dim_qe = 768


[deepseek-ai]
cosmosai_llm_base_url = https://aiplatform.dev52.cbf.dev.paypalinc.com
model_id = deepseek-r1-dis-9233d

[llama-2-13b-chat]
cosmosai_llm_base_url = https://aiplatform.dev51.cbf.dev.paypalinc.com
model_id = llama-2-13b-cha-7b9ed

[llama-3.1-8b-instruct]
cosmosai_llm_base_url = https://aiplatform.dev51.cbf.dev.paypalinc.com
model_id = llama-3-1-8b-in-0ea04

[cosmosai]
api_key = 509ab1cfb7fe1b4921ade37f03faae76029f2bc3b472d5e0af887f76a9f4491b
base_url = https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1

[milvus]
uri = http://*************:19530
db = ai_localization
db_qe = ai_localization_qe