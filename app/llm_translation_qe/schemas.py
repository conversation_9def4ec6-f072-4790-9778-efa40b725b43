from typing import Optional, Literal

from pydantic import BaseModel

from app.llm import llm_models_list


class LLMConfiguration(BaseModel):
    model_name: str = llm_models_list[0]
    temperature: float = 0.0
    top_p: float = 0.5


class TextContentPart(BaseModel):
    type: Literal["text"]
    text: str


class ImageURL(BaseModel):
    url: str


class ImageContentPart(BaseModel):
    type: Literal["image_url"]
    image_url: ImageURL


class File(BaseModel):
    file_data: str


class FileContentPart(BaseModel):
    type: Literal["file"]
    file: File


class TemplateMessage(BaseModel):
    role: Literal["system", "user", "assistant"]
    content: str | list[TextContentPart | ImageContentPart | FileContentPart]


class TemplateVariable(BaseModel):
    key: str
    value: str


class OutputSchemaItem(BaseModel):
    name: str
    type: str
    description: str
    required: bool


class PromptTemplate(BaseModel):
    template_messages: list[TemplateMessage]
    template_variables: list[TemplateVariable] = []
    llm_configuration: LLMConfiguration
    output_schema: list[OutputSchemaItem] = []
    json_schema: Optional[dict[str, object]] = None


class RAGConfig(BaseModel):
    collection_name: str
    texts: list[str] = []  # Default to empty list, will be populated during processing
    limit: int = 5  # Default limit
    placeholder_key: str = "glossary"  # Default placeholder key


class ChatLLMRequest(BaseModel):
    prompt_template: PromptTemplate
    rag_configs: list[RAGConfig] = []


class ChatLLMResponse(BaseModel):
    is_json: bool
    llm_output: str | dict


class EvaluateTXLFResponse(BaseModel):
    gcs_url: str
    success: bool = True
    message: str = ""


class EvaluateExcelRequest(BaseModel):
    prompt_template: PromptTemplate
    rag_configs: list[RAGConfig] = []


class EvaluateExcelResponse(BaseModel):
    file_path: str
    download_url: str
    segments_evaluated: int
    success: bool = True
    message: str = ""
