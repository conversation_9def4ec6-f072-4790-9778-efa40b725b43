from typing import Optional

from app.llm_translation_qe.schemas import OutputSchemaItem


def build_structured_output_schema(
    schema_items: Optional[list[OutputSchemaItem]],
) -> Optional[dict]:
    if schema_items:
        required_properties = []
        properties = {}
        for schema_item in schema_items:
            properties[schema_item.name] = {
                "type": schema_item.type,
                "description": schema_item.description,
            }
            if schema_item.required:
                required_properties.append(schema_item.name)

        return {
            "type": "object",
            "properties": properties,
            "required": required_properties,
        }
    else:
        return None
