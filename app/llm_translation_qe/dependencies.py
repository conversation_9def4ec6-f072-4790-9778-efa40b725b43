import json
from typing import Annotated

from fastapi import Form
from loguru import logger
from pydantic import ValidationError

from app.llm_translation_qe.schemas import PromptTemplate


def get_prompt_template(
        prompt_template_json: Annotated[str, Form(...)],
) -> PromptTemplate:
    """
    Dependency to parse and validate the prompt template JSON.
    """
    try:
        prompt_template_dict = json.loads(prompt_template_json)
        return PromptTemplate(**prompt_template_dict)
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format for prompt_template_json: {e}")
        raise ValueError(f"Invalid JSON format for prompt_template_json: {e}")
    except ValidationError as e:
        logger.error(f"Validation error for prompt_template_json: {e}")
        raise ValueError(f"Validation error for prompt_template_json: {e}")
