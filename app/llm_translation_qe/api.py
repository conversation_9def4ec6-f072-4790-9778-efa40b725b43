import asyncio
import os
import uuid
import threading
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from loguru import logger
from starlette import status
from typing import Dict

from app.llm import llm_models_list
from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.excel_evaluator import ExcelTranslationEvaluator
from app.llm_translation_qe.schemas import (
    ChatLLMRequest,
    ChatLLMResponse,
    EvaluateExcelRequest,
    EvaluateExcelResponse,
)

llm_translation_qe_api_router = APIRouter(prefix="/llm-translation-qe")

# In-memory job store (for demo; use Redis/db for production)
job_store: Dict[str, dict] = {}


@llm_translation_qe_api_router.get("/llm-models")
def get_llm_models():
    return llm_models_list


@llm_translation_qe_api_router.post("/call-llm", response_model=ChatLLMResponse)
async def call_llm(chat_llm_request: ChatLLMRequest):
    try:
        llm_translation_evaluator = LLMTranslationEvaluator()
        prompt_template = chat_llm_request.prompt_template
        rag_configs = chat_llm_request.rag_configs
        llm_output = await llm_translation_evaluator.call_llm(
            prompt_template, rag_configs
        )

        if isinstance(llm_output, dict):
            is_json = True
        else:
            is_json = False

        return ChatLLMResponse(is_json=is_json, llm_output=llm_output)
    except Exception as e:
        logger.exception("An error occurs when calling LLM.", e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@llm_translation_qe_api_router.post("/evaluate-excel-file", response_model=None)
async def evaluate_excel_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    prompt_json: str = Form(...),
):
    """
    Accepts the file and parameters, starts background job, returns job_id immediately.
    """
    try:
        temp_dir = "/tmp/excel_evaluation"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = f"{temp_dir}/{file.filename}"
        with open(temp_file_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # Parse prompt_json
        import json
        from pydantic import TypeAdapter
        adapter = TypeAdapter(EvaluateExcelRequest)
        request_data = adapter.validate_json(prompt_json)
        prompt_template = request_data.prompt_template
        rag_configs = request_data.rag_configs

        # Generate job_id
        job_id = str(uuid.uuid4())
        job_store[job_id] = {"status": "pending", "result": None}

        def run_evaluation():
            try:
                excel_evaluator = ExcelTranslationEvaluator()
                output_path, segments_evaluated = asyncio.run(
                    excel_evaluator.evaluate_excel_file(
                        file_path=temp_file_path,
                        prompt_template=prompt_template,
                        rag_configs=rag_configs,
                        output_dir=temp_dir
                    )
                )
                file_name = os.path.basename(output_path)
                download_url = f"/llm-translation-qe/download-excel/{file_name}"
                job_store[job_id] = {
                    "status": "completed",
                    "result": {
                        "file_path": output_path,
                        "download_url": download_url,
                        "segments_evaluated": segments_evaluated,
                        "success": True,
                        "message": "Excel file evaluated successfully"
                    }
                }
                # Cleanup temp file
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            except Exception as e:
                job_store[job_id] = {"status": "failed", "result": {"success": False, "message": str(e)}}

        # Start background thread
        threading.Thread(target=run_evaluation, daemon=True).start()

        return JSONResponse({"job_id": job_id})
    except Exception as e:
        logger.exception("An error occurred when evaluating Excel file.", e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@llm_translation_qe_api_router.get("/evaluate-excel-status/{job_id}")
def get_evaluate_excel_status(job_id: str):
    job = job_store.get(job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job


@llm_translation_qe_api_router.get("/download-excel/{file_name}")
async def download_excel_file(file_name: str):
    """
    Downloads an evaluated Excel file.

    Args:
        file_name: Name of the file to download

    Returns:
        Excel file as a download
    """
    try:
        file_path = f"/tmp/excel_evaluation/{file_name}"

        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_name} not found"
            )

        return FileResponse(
            path=file_path,
            filename=file_name,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error downloading file {file_name}", e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
