import re
from typing import Optional

from loguru import logger

from app.llm.gemini_llm import <PERSON><PERSON><PERSON>
from app.llm.llm_factory import LLMFactory
from app.llm_translation_qe.schemas import (
    PromptTemplate,
    TemplateMessage,
    TemplateVariable,
    OutputSchemaItem,
    LLMConfiguration,
    RAGConfig,
)
from app.rag.service import RAGService


class LLMTranslationEvaluator:
    def __init__(self):
        self.milvus_uri = "http://10.183.34.136:19530"
        self.milvus_db = "ai_localization"

    async def call_llm(
        self, prompt_template: PromptTemplate, rag_configs: list[RAGConfig]
    ) -> str | dict:
        messages = self.__process_messages(
            prompt_template.template_messages,
            prompt_template.template_variables,
            prompt_template.llm_configuration,
        )

        if len(rag_configs) > 0:
            messages = await self.__augment_messages(messages, rag_configs)

        llm_configuration = prompt_template.llm_configuration
        structured_output_schema = self.__build_structured_output_schema(
            prompt_template.output_schema
        )

        if prompt_template.json_schema is not None:
            structured_output_schema = prompt_template.json_schema

        llm = LLMFactory.build_llm(model_name=llm_configuration.model_name)

        if isinstance(llm, GeminiLLM):
            llm_output = await llm.async_chat(
                messages=messages,
                llm_configuration=llm_configuration.model_dump(),
                structured_output_schema=structured_output_schema,
            )
        else:
            llm_output = llm.chat(
                messages=messages,
                llm_configuration=llm_configuration.model_dump(),
                structured_output_schema=structured_output_schema,
            )

        return llm_output

    @staticmethod
    async def __augment_messages(
        messages: list[dict], rag_configs: list[RAGConfig]
    ) -> list[dict]:
        placeholder_variables_dict = {}
        for rag_config in rag_configs:
            collection_name = rag_config.collection_name
            texts = rag_config.texts
            limit = rag_config.limit
            placeholder_key = rag_config.placeholder_key

            search_results = await RAGService().search(
                collection_name=collection_name, texts=texts, limit=limit
            )

            search_results_text = ""
            for search_result in search_results:
                search_results_text += "\n".join(
                    [item["text_for_retrieval"] for item in search_result]
                )
                search_results_text += "\n"

            logger.info(
                f"Search results from collection {collection_name}:\n{search_results_text}"
            )
            placeholder_variables_dict[placeholder_key] = search_results_text

        def replace_match(match):
            variable_name = match.group(1)
            return placeholder_variables_dict.get(variable_name, match.group(0))

        pattern = r"\{\{([^}]+)\}\}"
        augmented_messages = []
        for message in messages:
            role = message["role"]
            if isinstance(message["content"], str):
                content = re.sub(pattern, replace_match, message["content"])
            else:
                content = []
                for part in message["content"]:
                    if part["type"] == "text":
                        part["text"] = re.sub(pattern, replace_match, part["text"])
                    content.append(part)
            augmented_messages.append({"role": role, "content": content})

        return augmented_messages

    @staticmethod
    def __process_messages(
        template_messages: list[TemplateMessage],
        template_variables: list[TemplateVariable],
        llm_configuration: LLMConfiguration,
    ):
        template_variables_dict = {}
        for template_variable in template_variables:
            template_variables_dict[template_variable.key] = template_variable.value

        def replace_match(match):
            variable_name = match.group(1)
            return template_variables_dict.get(variable_name, match.group(0))

        pattern = r"\{\{([^}]+)\}\}"
        processed_messages = []
        for template_message in template_messages:
            template_message_dict = template_message.model_dump()
            role = template_message_dict["role"]

            if isinstance(template_message_dict["content"], str):
                content = re.sub(
                    pattern, replace_match, template_message_dict["content"]
                )
            else:
                if "google" not in llm_configuration.model_name:
                    raise RuntimeError(
                        f"Model {llm_configuration.model_name} only supports string-type message content, "
                        f"but got: {template_message_dict['content']}"
                    )
                content = []
                for part in template_message_dict["content"]:
                    if part["type"] == "text":
                        part["text"] = re.sub(pattern, replace_match, part["text"])
                    content.append(part)

            processed_messages.append({"role": role, "content": content})

        return processed_messages

    @staticmethod
    def __build_structured_output_schema(
        output_schema: list[OutputSchemaItem],
    ) -> Optional[dict]:
        if output_schema:
            required_properties = []
            properties = {}
            for schema_item in output_schema:
                properties[schema_item.name] = {
                    "type": schema_item.type,
                    "description": schema_item.description,
                }
                if schema_item.required:
                    required_properties.append(schema_item.name)

            return {
                "type": "object",
                "properties": properties,
                "required": required_properties,
            }
        else:
            return None

    def chunk_text(self, text: str, max_terms: int = 5, min_terms: int = 1) -> list[str]:
        """
        Processes text for glossary term matching by creating appropriate chunks.

        For glossary matching, we want to create chunks that are likely to contain complete terms.
        This method creates chunks based on words/terms rather than character count.

        Args:
            text: The text to process (typically a sentence)
            max_terms: Maximum number of terms to include in a chunk
            min_terms: Minimum number of terms to include in a chunk

        Returns:
            List of text chunks optimized for glossary matching
        """
        # For very short text, just return it as is
        if not text or len(text.strip()) == 0:
            return []

        # Clean and normalize the text
        # Remove extra whitespace and normalize punctuation
        text = text.strip()

        # If the text is very short (likely a single term), return it as is
        if len(text.split()) <= max_terms:
            return [text]

        # Split the text into words
        words = text.split()

        # Create chunks of words with sliding window
        chunks = []

        # Single words (important for exact term matching)
        for word in words:
            # Skip very short words (like 'a', 'an', 'the', etc.)
            if len(word) > 2 and not word.lower() in ['the', 'and', 'for', 'but', 'nor', 'yet', 'so', 'at', 'by', 'to']:
                chunks.append(word)

        # Bigrams (pairs of words)
        for i in range(len(words) - 1):
            bigram = f"{words[i]} {words[i+1]}"
            chunks.append(bigram)

        # Trigrams (triplets of words)
        for i in range(len(words) - 2):
            trigram = f"{words[i]} {words[i+1]} {words[i+2]}"
            chunks.append(trigram)

        # Add larger n-grams up to max_terms
        if max_terms > 3:
            for n in range(4, min(max_terms + 1, len(words) + 1)):
                for i in range(len(words) - n + 1):
                    n_gram = " ".join(words[i:i+n])
                    chunks.append(n_gram)

        # Add the full text as a chunk for context
        if text not in chunks:
            chunks.append(text)

        # Remove duplicates while preserving order
        unique_chunks = []
        for chunk in chunks:
            if chunk not in unique_chunks:
                unique_chunks.append(chunk)

        return unique_chunks
