import os
import time
from typing import Dict, <PERSON>, Optional, Tu<PERSON>, Any
import pandas as pd
from loguru import logger

from app.llm_translation_qe.llm_translation_evaluator import LLMTranslationEvaluator
from app.llm_translation_qe.schemas import (
    PromptTemplate,
    TemplateMessage,
    TemplateVariable,
    OutputSchemaItem,
    LLMConfiguration,
    RAGConfig,
)


class ExcelTranslationEvaluator:
    """
    Class to evaluate translation quality in Excel files.
    """
    def __init__(self):
        self.llm_evaluator = LLMTranslationEvaluator()

    async def evaluate_excel_file(
        self,
        file_path: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig],
        output_dir: str = "/tmp"
    ) -> Tuple[str, int]:
        """
        Evaluates translation quality for all segments in an Excel file.

        Args:
            file_path: Path to the Excel file
            prompt_template: Prompt template for LLM evaluation
            rag_configs: RAG configurations for LLM evaluation
            output_dir: Directory to save the output file

        Returns:
            Tuple of (output file path, number of segments evaluated)
        """
        logger.info(f"Evaluating Excel file: {file_path}")

        # Read the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist (case-insensitive match)
        required_columns = ["Segment Source", "Segment Target"]
        # Build a mapping from lower-case column names to actual column names
        col_map = {col.lower(): col for col in df.columns}
        for col in required_columns:
            if col.lower() not in col_map:
                raise ValueError(f"Required column '{col}' not found in the Excel file")

        # Ensure output columns exist with correct dtype (object for string compatibility), case-insensitive
        output_columns = ["QE Score", "Error Category", "Error Severity", "Error Comment"]
        for col in output_columns:
            if col.lower() not in col_map:
                df[col] = None
                df[col] = df[col].astype(object)
                col_map[col.lower()] = col  # Add to col_map
            else:
                actual_col = col_map[col.lower()]
                if df[actual_col].dtype != object:
                    df[actual_col] = df[actual_col].astype(object)

        # Use the actual column names for source/target/output columns
        source_col = col_map["segment source"]
        target_col = col_map["segment target"]
        qe_score_col = col_map["qe score"]
        error_category_col = col_map["error category"]
        error_severity_col = col_map["error severity"]
        error_comment_col = col_map["error comment"]

        # Process each row
        total_segments = len(df)
        logger.info(f"Found {total_segments} segments to evaluate")

        segment_num = 0
        for idx, row in df.iterrows():
            segment_num += 1
            source_text = row[source_col]
            target_text = row[target_col]

            # Skip if either source or target is empty
            if pd.isna(source_text) or pd.isna(target_text) or not source_text or not target_text:
                logger.warning(f"Skipping row {segment_num}: Empty source or target text")
                continue

            logger.info(f"Evaluating segment {segment_num}/{total_segments}")

            # Create a copy of the prompt template with the current segment
            segment_prompt = self._create_segment_prompt(prompt_template, source_text, target_text)

            # Update RAG configs with the source text chunks
            updated_rag_configs = self._update_rag_configs(rag_configs, source_text)

            try:
                # Call LLM to evaluate the segment
                evaluation_result = await self.llm_evaluator.call_llm(segment_prompt, updated_rag_configs)

                # Process the evaluation result
                if isinstance(evaluation_result, dict):
                    # Update the dataframe with the evaluation results
                    df.at[idx, qe_score_col] = evaluation_result.get("score", None)
                    df.at[idx, error_category_col] = evaluation_result.get("error_category", None)
                    df.at[idx, error_severity_col] = evaluation_result.get("error_severity", None)
                    df.at[idx, error_comment_col] = evaluation_result.get("comment", None)

                    logger.info(f"Segment {segment_num} evaluated: Score={evaluation_result.get('score', 'N/A')}, "
                               f"Category={evaluation_result.get('error_category', 'N/A')}")
                else:
                    logger.warning(f"Unexpected evaluation result format for segment {segment_num}: {evaluation_result}")
            except Exception as e:
                error_msg = f"Error evaluating segment {segment_num}: {e}"
                logger.error(error_msg)
                raise Exception(error_msg)

        # Generate output file path
        timestamp = int(time.time())
        file_name = os.path.basename(file_path)
        base_name, ext = os.path.splitext(file_name)
        output_file_name = f"{base_name}_evaluated_{timestamp}{ext}"
        output_path = os.path.join(output_dir, output_file_name)

        # Save the updated dataframe to Excel
        df.to_excel(output_path, index=False)
        logger.info(f"Saved evaluated file to {output_path}")

        return output_path, total_segments

    def _create_segment_prompt(
        self,
        prompt_template: PromptTemplate,
        source_text: str,
        target_text: str
    ) -> PromptTemplate:
        """
        Creates a copy of the prompt template with the current segment.

        Args:
            prompt_template: Original prompt template
            source_text: Source text for the current segment
            target_text: Target text for the current segment

        Returns:
            Updated prompt template with segment variables
        """
        # Ensure source_text and target_text are strings
        if not isinstance(source_text, str):
            source_text = str(source_text)
        if not isinstance(target_text, str):
            target_text = str(target_text)

        # Create a copy of the prompt template
        segment_prompt = PromptTemplate(
            template_messages=prompt_template.template_messages,
            llm_configuration=prompt_template.llm_configuration,
            output_schema=prompt_template.output_schema,
            json_schema=prompt_template.json_schema
        )

        # Add segment variables
        segment_variables = [
            TemplateVariable(key="source_text", value=source_text),
            TemplateVariable(key="target_text", value=target_text)
        ]

        # Combine with existing variables, overriding if needed
        existing_vars = {var.key: var.value for var in prompt_template.template_variables}
        for var in segment_variables:
            existing_vars[var.key] = var.value

        segment_prompt.template_variables = [
            TemplateVariable(key=k, value=v) for k, v in existing_vars.items()
        ]

        return segment_prompt

    def _update_rag_configs(self, rag_configs: List[RAGConfig], source_text: str) -> List[RAGConfig]:
        """
        Updates RAG configs with source text chunks optimized for glossary matching.

        Args:
            rag_configs: Original RAG configs from the frontend
            source_text: Source text for the current segment

        Returns:
            Updated RAG configs with source text chunks
        """
        # Ensure source_text is a string before processing
        if not isinstance(source_text, str):
            source_text = str(source_text)

        if not rag_configs:
            logger.info("No RAG configs provided. Skipping RAG processing.")
            return []

        updated_configs = []

        for config in rag_configs:
            # Create a copy of the config with updated texts
            updated_config = config.model_copy(update={
                "texts": []  # Will be updated with source text chunks
            })

            # Process the source text for glossary matching
            logger.info(f"Processing source text for glossary matching: '{source_text}'")

            # Generate chunks optimized for glossary term matching
            source_chunks = self.llm_evaluator.chunk_text(source_text, max_terms=5, min_terms=1)

            if source_chunks:
                logger.info(f"Generated {len(source_chunks)} chunks for glossary matching: {source_chunks}")
                updated_config.texts = source_chunks
            else:
                # Fallback: if no chunks were generated, use the original text
                logger.warning(f"No chunks generated for source text. Using original text as fallback.")
                updated_config.texts = [source_text] if source_text else []

            updated_configs.append(updated_config)

        return updated_configs

