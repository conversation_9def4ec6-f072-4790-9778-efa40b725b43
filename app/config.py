import configparser


class Config:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls, *args, **kwargs)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.config_parser = configparser.ConfigParser()
            self.config_parser.read("app/config.ini")
            self._initialized = True

    def get(self, section, option):
        return self.config_parser.get(section, option)
