import json
import re
from typing import Optional

from google import genai
from google.genai import types
from google.genai.types import HttpOptions

from app.llm.base_llm import BaseLLM


class GeminiLLM(BaseLLM):
    def __init__(self, project_id: str, location: str):
        self.genai_client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
            http_options=HttpOptions(api_version="v1"),
        )

    async def async_chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        google_genai_messages = []
        system_instruction = None
        for idx, message in enumerate(messages):
            if message["role"] == "system":
                if idx != 0:
                    raise RuntimeError(
                        f"You can only provide one system message at the start of the messages!"
                    )
                else:
                    system_instruction = message["content"]

            elif message["role"] == "user":
                content = message["content"]
                if isinstance(content, str):
                    google_genai_messages.append(
                        types.UserContent(
                            parts=[types.Part.from_text(text=message["content"])]
                        )
                    )
                elif isinstance(content, list):
                    parts = []
                    for content_part in content:
                        if content_part["type"] == "text":
                            parts.append(
                                types.Part.from_text(text=content_part["text"])
                            )
                        elif content_part["type"] == "image_url":
                            image_url = content_part["image_url"]["url"]
                            mime_type, base64_data = self.extract_mime_type_and_base64(
                                image_url
                            )
                            if mime_type is None or base64_data is None:
                                raise ValueError(f"Invalid image URL: {image_url}!")
                            parts.append(
                                types.Part.from_bytes(
                                    data=base64_data,
                                    mime_type=mime_type,
                                )
                            )
                        elif content_part["type"] == "file":
                            file_data = content_part["file"]["file_data"]
                            mime_type, base64_data = self.extract_mime_type_and_base64(
                                file_data
                            )
                            if mime_type is None or base64_data is None:
                                raise ValueError(f"Invalid file data: {file_data}!")
                            parts.append(
                                types.Part.from_bytes(
                                    data=base64_data,
                                    mime_type=mime_type,
                                )
                            )
                        else:
                            raise ValueError(
                                f"Illegal type {content_part['type']} for message content part!"
                            )
                    google_genai_messages.append(types.UserContent(parts=parts))
                else:
                    raise TypeError(
                        f"Illegal type {type(content)} for message content!"
                    )

            elif message["role"] == "assistant":
                google_genai_messages.append(
                    types.ModelContent(
                        parts=[types.Part.from_text(text=message["content"])]
                    )
                )
            else:
                raise ValueError(f"Message role {message['role']} is not supported!")

        if structured_output_schema is not None:
            response_mime_type = "application/json"
            response_schema = structured_output_schema
        else:
            response_mime_type = None
            response_schema = None

        generation_config = types.GenerateContentConfig(
            system_instruction=system_instruction,
            temperature=llm_configuration["temperature"],
            top_p=llm_configuration["top_p"],
            response_mime_type=response_mime_type,
            response_schema=response_schema,
        )

        response = await self.genai_client.aio.models.generate_content(
            model=llm_configuration["model_name"],
            contents=google_genai_messages,
            config=generation_config,
        )

        if structured_output_schema is not None:
            llm_output = json.loads(response.text)
        else:
            llm_output = response.text

        return llm_output

    def chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        google_genai_messages = []
        system_instruction = None
        for idx, message in enumerate(messages):
            if message["role"] == "system":
                if idx != 0:
                    raise RuntimeError(
                        f"You can only provide one system message at the start of the messages!"
                    )
                else:
                    system_instruction = message["content"]

            elif message["role"] == "user":
                content = message["content"]
                if isinstance(content, str):
                    google_genai_messages.append(
                        types.UserContent(
                            parts=[types.Part.from_text(text=message["content"])]
                        )
                    )
                elif isinstance(content, list):
                    parts = []
                    for content_part in content:
                        if content_part["type"] == "text":
                            parts.append(
                                types.Part.from_text(text=content_part["text"])
                            )
                        elif content_part["type"] == "image_url":
                            image_url = content_part["image_url"]["url"]
                            mime_type, base64_data = self.extract_mime_type_and_base64(
                                image_url
                            )
                            if mime_type is None or base64_data is None:
                                raise ValueError(f"Invalid image URL: {image_url}!")
                            parts.append(
                                types.Part.from_bytes(
                                    data=base64_data,
                                    mime_type=mime_type,
                                )
                            )
                        elif content_part["type"] == "file":
                            file_data = content_part["file"]["file_data"]
                            mime_type, base64_data = self.extract_mime_type_and_base64(
                                file_data
                            )
                            if mime_type is None or base64_data is None:
                                raise ValueError(f"Invalid file data: {file_data}!")
                            parts.append(
                                types.Part.from_bytes(
                                    data=base64_data,
                                    mime_type=mime_type,
                                )
                            )
                        else:
                            raise ValueError(
                                f"Illegal type {content_part['type']} for message content part!"
                            )
                    google_genai_messages.append(types.UserContent(parts=parts))
                else:
                    raise TypeError(
                        f"Illegal type {type(content)} for message content!"
                    )

            elif message["role"] == "assistant":
                google_genai_messages.append(
                    types.ModelContent(
                        parts=[types.Part.from_text(text=message["content"])]
                    )
                )
            else:
                raise ValueError(f"Message role {message['role']} is not supported!")

        if structured_output_schema is not None:
            response_mime_type = "application/json"
            response_schema = structured_output_schema
        else:
            response_mime_type = None
            response_schema = None

        generation_config = types.GenerateContentConfig(
            system_instruction=system_instruction,
            temperature=llm_configuration["temperature"],
            top_p=llm_configuration["top_p"],
            response_mime_type=response_mime_type,
            response_schema=response_schema,
        )

        response = self.genai_client.models.generate_content(
            model=llm_configuration["model_name"],
            contents=google_genai_messages,
            config=generation_config,
        )

        if structured_output_schema is not None:
            llm_output = json.loads(response.text)
        else:
            llm_output = response.text

        return llm_output

    @staticmethod
    def extract_mime_type_and_base64(
        data_uri: str,
    ) -> tuple[Optional[str], Optional[str]]:
        match = re.match(r"data:([\w/\-+.]+);base64,(.+)", data_uri)
        if match:
            mime_type = match.group(1)
            base64_data = match.group(2)
            return mime_type, base64_data
        return None, None
