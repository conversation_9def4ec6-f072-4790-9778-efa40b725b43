import json
from typing import Optional

import openai

from app.llm.base_llm import BaseLLM


class CosmosAILLM(BaseLLM):
    def __init__(self, cosmosai_llm_base_url: str, model_id: str):
        self.cosmosai_llm_base_url = cosmosai_llm_base_url
        self.model_id = model_id

    @property
    def openai_client(self):
        base_url = f"{self.cosmosai_llm_base_url}/seldon/seldon/{self.model_id}/v2/models/{self.model_id}"
        client = openai.OpenAI(base_url=base_url, api_key="xxx")
        return client

    async def async_chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        raise NotImplementedError

    def chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        client = self.openai_client
        if structured_output_schema is not None:
            extra_body = {"guided_json": structured_output_schema}
        else:
            extra_body = None

        response = client.chat.completions.create(
            model=llm_configuration["model_name"],
            messages=messages,
            temperature=llm_configuration["temperature"],
            top_p=llm_configuration["top_p"],
            extra_body=extra_body,
        )

        if structured_output_schema is not None:
            llm_output = json.loads(response.choices[0].message.content)
        else:
            llm_output = response.choices[0].message.content

        return llm_output
