from app.config import Config
from app.llm import llm_models_list
from app.llm.base_llm import BaseLLM
from app.llm.cosmosai_llm import CosmosAILLM
from app.llm.new_cosmosai_llm import CosmosAILLM as NewCosmosAILLM
from app.llm.gemini_llm import GeminiLLM


class LLMFactory:
    # Define the new CosmosAI models
    NEW_COSMOSAI_MODELS = {
        "claude-3-7-sonnet-20250219",
        "claude-3-5-haiku-20241022",
        "claude-sonnet-4-20250514",
        "llama33-70b",
        "llama31-8b",
        "gpt-4.1",
        "gpt-4o",
        "gpt-4o-mini",
        "qwen25-coder-32b"
    }

    @classmethod
    def build_llm(cls, model_name: str) -> BaseLLM:
        # Handle Google/Gemini models
        if "google/" in model_name:
            project_id = Config().get("google", "project_id")
            location = Config().get("google", "location")
            return GeminiLLM(project_id=project_id, location=location)

        # Handle new CosmosAI models (OpenAI-compatible API)
        elif model_name in cls.NEW_COSMOSAI_MODELS:
            return NewCosmosAILLM(model_name=model_name)

        # Handle legacy CosmosAI models (old API format)
        elif model_name == "deepseek-ai/DeepSeek-R1-Distill-Llama-70B":
            cosmosai_llm_base_url = Config().get("deepseek-ai", "cosmosai_llm_base_url")
            model_id = Config().get("deepseek-ai", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        elif model_name == "llama-2-13b-chat":
            cosmosai_llm_base_url = Config().get(
                "llama-2-13b-chat", "cosmosai_llm_base_url"
            )
            model_id = Config().get("llama-2-13b-chat", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        elif model_name == "llama-3.1-8b-instruct":
            cosmosai_llm_base_url = Config().get(
                "llama-3.1-8b-instruct", "cosmosai_llm_base_url"
            )
            model_id = Config().get("llama-3.1-8b-instruct", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        else:
            raise ValueError(
                f"Model: {model_name} is not supported! Supported model name: {llm_models_list}"
            )
