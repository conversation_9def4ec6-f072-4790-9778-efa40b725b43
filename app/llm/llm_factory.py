from app.config import Config
from app.llm import llm_models_list
from app.llm.base_llm import BaseLLM
from app.llm.cosmosai_llm import CosmosAILLM
from app.llm.new_cosmosai_llm import CosmosAILLM as NewCosmosAILLM
from app.llm.gemini_llm import GeminiLLM


class LLMFactory:
    @classmethod
    def build_llm(cls, model_name: str) -> BaseLLM:
        if "google/" in model_name:
            project_id = Config().get("google", "project_id")
            location = Config().get("google", "location")
            return GeminiLLM(project_id=project_id, location=location)
        elif model_name == "deepseek-ai/DeepSeek-R1-Distill-Llama-70B":
            cosmosai_llm_base_url = Config().get("deepseek-ai", "cosmosai_llm_base_url")
            model_id = Config().get("deepseek-ai", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        elif model_name == "llama-2-13b-chat":
            cosmosai_llm_base_url = Config().get(
                "llama-2-13b-chat", "cosmosai_llm_base_url"
            )
            model_id = Config().get("llama-2-13b-chat", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        elif model_name == "llama-3.1-8b-instruct":
            cosmosai_llm_base_url = Config().get(
                "llama-3.1-8b-instruct", "cosmosai_llm_base_url"
            )
            model_id = Config().get("llama-3.1-8b-instruct", "model_id")
            return CosmosAILLM(
                cosmosai_llm_base_url=cosmosai_llm_base_url, model_id=model_id
            )
        else:
            raise ValueError(
                f"Model: {model_name} is not supported! Supported model name: {llm_models_list}"
            )
