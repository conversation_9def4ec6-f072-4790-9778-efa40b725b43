llm_models_list = [
    # Google/Gemini models
    "google/gemini-2.0-flash",
    "google/gemini-1.5-flash",
    "google/gemini-1.5-pro",
    "google/gemini-2.5-pro-preview-03-25",
    "google/gemini-2.0-flash-thinking-exp-01-21",

    # New CosmosAI models (OpenAI-compatible API)
    "claude-3-7-sonnet-20250219",
    "claude-3-5-haiku-20241022",
    "claude-sonnet-4-20250514",
    "llama33-70b",
    "llama31-8b",
    "gpt-4.1",
    "gpt-4o",
    "gpt-4o-mini",
    "deepseek-r1-distill-llama-70b",
    "deepseek-r1-distill-qwen-32b",
    "qwen25-coder-32b",
    "gemini-2.5-pro-preview-03-25",
    "gemini-2.0-flash",
    "gemini-2.0-flash-lite",

    # Legacy CosmosAI models (old API format)
    "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
    "llama-3.1-8b-instruct",
    "llama-2-13b-chat",
]
