"""
CosmosAI LLM implementation using OpenAI-compatible API.
"""
import json
from typing import Optional, Any

from openai import OpenAI
from loguru import logger

from app.llm.base_llm import BaseLLM


class CosmosAILLM(BaseLLM):
    """
    CosmosAI LLM implementation using OpenAI-compatible API.
    
    Supports multiple models including Claude, GPT, Llama, Gemini, and others
    through the CosmosAI platform.
    """
    
    def __init__(self, model_name: str):
        """
        Initialize CosmosAI LLM.

        Args:
            model_name: The model ID to use (e.g., 'claude-3-5-haiku-20241022', 'gpt-4o', etc.)
        """
        self.model_name = model_name
        
        # CosmosAI configuration
        self.api_key = self._get_api_key()
        self.base_url = "https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1"
        
        # Initialize OpenAI client with CosmosAI endpoint
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        # Supported models
        self.supported_models = {
            "claude-3-7-sonnet-20250219",
            "claude-3-5-haiku-20241022", 
            "claude-sonnet-4-20250514",
            "llama33-70b",
            "llama31-8b",
            "gpt-4.1",
            "gpt-4o",
            "gpt-4o-mini",
            "qwen25-coder-32b"
        }
        
        if model_name not in self.supported_models:
            logger.warning(f"Model {model_name} not in supported models list. Attempting to use anyway.")
    
    def _get_api_key(self) -> str:
        """
        Get the API key from environment variables or config.
        
        Returns:
            str: The API key
        """
        import os
        from app.config import Config
        
        # Try to get from environment variable first
        api_key = os.environ.get("COSMOSAI_API_KEY")
        
        if not api_key:
            # Try to get from config file
            try:
                config = Config()
                api_key = config.get("cosmosai", "api_key")
            except Exception as e:
                logger.warning(f"Could not get API key from config: {e}")
        
        if not api_key:
            logger.error("CosmosAI API key not found. Please set COSMOSAI_API_KEY environment variable or add to config.")
            raise ValueError("CosmosAI API key not configured")
        
        return api_key
    
    def _format_messages(self, messages: list[dict]) -> list[dict]:
        """
        Format messages for CosmosAI API.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List[Dict[str, str]]: Formatted messages
        """
        formatted_messages = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            # Ensure role is valid
            if role not in ["system", "user", "assistant"]:
                role = "user"
            
            formatted_messages.append({
                "role": role,
                "content": content
            })
        
        return formatted_messages
    
    def _extract_response_content(self, response) -> str:
        """
        Extract content from CosmosAI response.

        Args:
            response: The response from CosmosAI API

        Returns:
            str: Extracted content
        """
        try:
            if hasattr(response, 'choices') and len(response.choices) > 0:
                choice = response.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    return choice.message.content.strip()

            logger.warning("Unexpected response format from CosmosAI")
            return ""
        except Exception as e:
            logger.error(f"Error extracting response content: {e}")
            return ""

    def _create_schema_instruction(self, schema: dict) -> str:
        """
        Create instruction text from JSON schema.

        Args:
            schema: JSON schema dictionary

        Returns:
            str: Instruction text for the schema
        """
        try:
            instruction = "Please respond with a valid JSON object that matches this schema:\n\n"
            instruction += json.dumps(schema, indent=2)
            instruction += "\n\nEnsure your response is valid JSON and includes all required fields."
            return instruction
        except Exception as e:
            logger.error(f"Error creating schema instruction: {e}")
            return "Please respond with a valid JSON object."

    def _add_schema_instruction(self, messages: list[dict], schema_instruction: str) -> list[dict]:
        """
        Add schema instruction to messages.

        Args:
            messages: List of message dictionaries
            schema_instruction: Schema instruction text

        Returns:
            list[dict]: Updated messages with schema instruction
        """
        try:
            # Find the last user message and append the schema instruction
            updated_messages = messages.copy()

            for i in range(len(updated_messages) - 1, -1, -1):
                if updated_messages[i].get("role") == "user":
                    # Append schema instruction to the last user message
                    updated_messages[i]["content"] += f"\n\n{schema_instruction}"
                    break
            else:
                # If no user message found, add as a new user message
                updated_messages.append({
                    "role": "user",
                    "content": schema_instruction
                })

            return updated_messages
        except Exception as e:
            logger.error(f"Error adding schema instruction: {e}")
            return messages

    def _extract_json_from_text(self, text: str) -> Optional[str]:
        """
        Extract JSON content from text that might contain other content.

        Args:
            text: Text that might contain JSON

        Returns:
            Optional[str]: Extracted JSON string or None
        """
        try:
            # Look for JSON object patterns
            import re

            # Try to find JSON objects in the text
            json_patterns = [
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Simple nested JSON
                r'\{.*?\}',  # Basic JSON object
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, text, re.DOTALL)
                for match in matches:
                    try:
                        # Test if it's valid JSON
                        json.loads(match)
                        return match
                    except json.JSONDecodeError:
                        continue

            return None
        except Exception as e:
            logger.error(f"Error extracting JSON from text: {e}")
            return None
    
    def chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        """
        Generate a chat completion using CosmosAI.
        
        Args:
            messages: List of message dictionaries
            llm_configuration: LLM configuration parameters
            structured_output_schema: Schema for structured output (if supported)
            
        Returns:
            str | Dict: Generated response or structured output
        """
        try:
            # Format messages
            formatted_messages = self._format_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.model_name,
                "messages": formatted_messages,
                "temperature": 0.7,  # Default temperature
                "max_tokens": 1024,  # Default max tokens
            }
            
            # Apply LLM configuration
            if "temperature" in llm_configuration:
                request_params["temperature"] = llm_configuration["temperature"]
            if "max_tokens" in llm_configuration:
                request_params["max_tokens"] = llm_configuration["max_tokens"]
            if "top_p" in llm_configuration:
                request_params["top_p"] = llm_configuration["top_p"]
            if "frequency_penalty" in llm_configuration:
                request_params["frequency_penalty"] = llm_configuration["frequency_penalty"]
            if "presence_penalty" in llm_configuration:
                request_params["presence_penalty"] = llm_configuration["presence_penalty"]
            
            # Handle structured output
            structured_output_requested = structured_output_schema is not None
            if structured_output_requested:
                # Add schema instructions to the messages
                schema_instruction = self._create_schema_instruction(structured_output_schema)
                formatted_messages = self._add_schema_instruction(formatted_messages, schema_instruction)
                request_params["messages"] = formatted_messages

                # Try to use JSON response format if supported
                try:
                    request_params["response_format"] = {"type": "json_object"}
                    logger.info("Using structured output with JSON format and schema instructions")
                except Exception as e:
                    logger.warning(f"JSON response format not supported for {self.model_name}: {e}")
                    logger.info("Will attempt to parse JSON from text response")
            
            logger.info(f"Making request to CosmosAI with model {self.model_name}")
            logger.debug(f"Request parameters: {request_params}")

            # Make the API call
            try:
                response = self.client.chat.completions.create(**request_params)
            except Exception as e:
                # If the request fails and we're using response_format, try without it
                if "response_format" in request_params and "unknown_parameter" in str(e).lower():
                    logger.warning(f"Response format not supported, retrying without it: {e}")
                    request_params_fallback = request_params.copy()
                    del request_params_fallback["response_format"]
                    response = self.client.chat.completions.create(**request_params_fallback)
                else:
                    raise
            
            # Extract response content
            content = self._extract_response_content(response)

            # If structured output was requested, try to parse as JSON
            if structured_output_requested and content:
                try:
                    # Try to parse the entire content as JSON
                    parsed_content = json.loads(content)
                    logger.info("Successfully parsed structured output")
                    return parsed_content
                except json.JSONDecodeError:
                    # Try to extract JSON from the content if it's embedded in text
                    try:
                        json_content = self._extract_json_from_text(content)
                        if json_content:
                            parsed_content = json.loads(json_content)
                            logger.info("Successfully extracted and parsed JSON from text response")
                            return parsed_content
                    except json.JSONDecodeError:
                        pass

                    logger.warning(f"Failed to parse structured output as JSON")
                    logger.warning(f"Raw content: {content}")
                    # Return raw content if JSON parsing fails
                    return content

            return content
            
        except Exception as e:
            logger.error(f"Error in CosmosAI chat completion: {e}")
            logger.error(f"Model: {self.model_name}, Messages: {messages}")
            raise
    
    async def async_chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        """
        Async version of chat completion.
        
        Args:
            messages: List of message dictionaries
            llm_configuration: LLM configuration parameters
            structured_output_schema: Schema for structured output (if supported)
            
        Returns:
            str | Dict: Generated response or structured output
        """
        try:
            # For now, we'll use the sync version wrapped in async
            # In the future, this could be updated to use async OpenAI client
            import asyncio
            
            # Run the sync chat method in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.chat,
                messages,
                llm_configuration,
                structured_output_schema
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in async CosmosAI chat completion: {e}")
            raise
    
    def get_available_models(self) -> list[str]:
        """
        Get list of available models from CosmosAI.
        
        Returns:
            List[str]: List of available model IDs
        """
        try:
            models_response = self.client.models.list()
            available_models = [model.id for model in models_response.data]
            logger.info(f"Retrieved {len(available_models)} available models from CosmosAI")
            return available_models
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            # Return the known supported models as fallback
            return list(self.supported_models)
    
    def test_model(self, test_message: str = "Hi, who are you?") -> bool:
        """
        Test if the model is working correctly.

        Args:
            test_message: Message to test with

        Returns:
            bool: True if model responds successfully, False otherwise
        """
        try:
            messages = [{"role": "user", "content": test_message}]
            config = {"max_tokens": 64, "temperature": 0}
            response = self.chat(messages, config)

            if response and len(str(response).strip()) > 0:
                logger.info(f"Model {self.model_name} test successful")
                return True
            else:
                logger.warning(f"Model {self.model_name} returned empty response")
                return False

        except Exception as e:
            logger.error(f"Model {self.model_name} test failed: {e}")
            return False
    
    @classmethod
    def get_model_info(cls) -> dict[str, Any]:
        """
        Get information about CosmosAI models.
        
        Returns:
            Dict[str, Any]: Model information
        """
        return {
            "provider": "CosmosAI",
            "base_url": "https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1",
            "api_type": "OpenAI-compatible",
            "supported_models": [
                "claude-3-7-sonnet-20250219",
                "claude-3-5-haiku-20241022",
                "claude-sonnet-4-20250514", 
                "llama33-70b",
                "llama31-8b",
                "gpt-4.1",
                "gpt-4o",
                "gpt-4o-mini",
                "qwen25-coder-32b"
            ],
            "features": [
                "chat_completion",
                "structured_output",
                "async_support",
                "multiple_providers"
            ]
        }
