"""
CosmosAI LLM implementation using OpenAI-compatible API.
"""
import json
from typing import Dict, List, Optional, Any

from openai import OpenAI
from loguru import logger

from app.llm.base_llm import BaseLLM


class CosmosAILLM(BaseLLM):
    """
    CosmosAI LLM implementation using OpenAI-compatible API.
    
    Supports multiple models including Claude, GPT, Llama, Gemini, and others
    through the CosmosAI platform.
    """
    
    def __init__(self, model_name: str):
        """
        Initialize CosmosAI LLM.

        Args:
            model_name: The model ID to use (e.g., 'claude-3-5-haiku-20241022', 'gpt-4o', etc.)
        """
        self.model_name = model_name
        
        # CosmosAI configuration
        self.api_key = self._get_api_key()
        self.base_url = "https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1"
        
        # Initialize OpenAI client with CosmosAI endpoint
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        # Supported models
        self.supported_models = {
            "claude-3-7-sonnet-20250219",
            "claude-3-5-haiku-20241022", 
            "claude-sonnet-4-20250514",
            "llama33-70b",
            "llama31-8b",
            "gpt-4.1",
            "gpt-4o",
            "gpt-4o-mini",
            "qwen25-coder-32b"
        }
        
        if model_name not in self.supported_models:
            logger.warning(f"Model {model_name} not in supported models list. Attempting to use anyway.")
    
    def _get_api_key(self) -> str:
        """
        Get the API key from environment variables or config.
        
        Returns:
            str: The API key
        """
        import os
        from app.config import Config
        
        # Try to get from environment variable first
        api_key = os.environ.get("COSMOSAI_API_KEY")
        
        if not api_key:
            # Try to get from config file
            try:
                config = Config()
                api_key = config.get("cosmosai", "api_key")
            except Exception as e:
                logger.warning(f"Could not get API key from config: {e}")
        
        if not api_key:
            logger.error("CosmosAI API key not found. Please set COSMOSAI_API_KEY environment variable or add to config.")
            raise ValueError("CosmosAI API key not configured")
        
        return api_key
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Format messages for CosmosAI API.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List[Dict[str, str]]: Formatted messages
        """
        formatted_messages = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            # Ensure role is valid
            if role not in ["system", "user", "assistant"]:
                role = "user"
            
            formatted_messages.append({
                "role": role,
                "content": content
            })
        
        return formatted_messages
    
    def _extract_response_content(self, response) -> str:
        """
        Extract content from CosmosAI response.
        
        Args:
            response: The response from CosmosAI API
            
        Returns:
            str: Extracted content
        """
        try:
            if hasattr(response, 'choices') and len(response.choices) > 0:
                choice = response.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    return choice.message.content.strip()
            
            logger.warning("Unexpected response format from CosmosAI")
            return ""
        except Exception as e:
            logger.error(f"Error extracting response content: {e}")
            return ""
    
    def chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        """
        Generate a chat completion using CosmosAI.
        
        Args:
            messages: List of message dictionaries
            llm_configuration: LLM configuration parameters
            structured_output_schema: Schema for structured output (if supported)
            
        Returns:
            str | Dict: Generated response or structured output
        """
        try:
            # Format messages
            formatted_messages = self._format_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.model_name,
                "messages": formatted_messages,
                "temperature": 0.7,  # Default temperature
                "max_tokens": 1024,  # Default max tokens
            }
            
            # Apply LLM configuration
            if "temperature" in llm_configuration:
                request_params["temperature"] = llm_configuration["temperature"]
            if "max_tokens" in llm_configuration:
                request_params["max_tokens"] = llm_configuration["max_tokens"]
            if "top_p" in llm_configuration:
                request_params["top_p"] = llm_configuration["top_p"]
            if "frequency_penalty" in llm_configuration:
                request_params["frequency_penalty"] = llm_configuration["frequency_penalty"]
            if "presence_penalty" in llm_configuration:
                request_params["presence_penalty"] = llm_configuration["presence_penalty"]
            
            # Handle structured output
            if structured_output_schema:
                # For models that support structured output, add response_format
                try:
                    request_params["response_format"] = {
                        "type": "json_object",
                        "schema": structured_output_schema
                    }
                    logger.info("Using structured output with JSON schema")
                except Exception as e:
                    logger.warning(f"Structured output not supported for {self.model_name}: {e}")
            
            logger.info(f"Making request to CosmosAI with model {self.model_name}")
            logger.debug(f"Request parameters: {request_params}")
            
            # Make the API call
            response = self.client.chat.completions.create(**request_params)
            
            # Extract response content
            content = self._extract_response_content(response)
            
            # If structured output was requested, try to parse as JSON
            if structured_output_schema and content:
                try:
                    parsed_content = json.loads(content)
                    logger.info("Successfully parsed structured output")
                    return parsed_content
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse structured output as JSON: {e}")
                    logger.warning(f"Raw content: {content}")
                    # Return raw content if JSON parsing fails
                    return content
            
            return content
            
        except Exception as e:
            logger.error(f"Error in CosmosAI chat completion: {e}")
            logger.error(f"Model: {self.model_name}, Messages: {messages}")
            raise
    
    async def async_chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        """
        Async version of chat completion.
        
        Args:
            messages: List of message dictionaries
            llm_configuration: LLM configuration parameters
            structured_output_schema: Schema for structured output (if supported)
            
        Returns:
            str | Dict: Generated response or structured output
        """
        try:
            # For now, we'll use the sync version wrapped in async
            # In the future, this could be updated to use async OpenAI client
            import asyncio
            
            # Run the sync chat method in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.chat,
                messages,
                llm_configuration,
                structured_output_schema
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in async CosmosAI chat completion: {e}")
            raise
    
    def get_available_models(self) -> List[str]:
        """
        Get list of available models from CosmosAI.
        
        Returns:
            List[str]: List of available model IDs
        """
        try:
            models_response = self.client.models.list()
            available_models = [model.id for model in models_response.data]
            logger.info(f"Retrieved {len(available_models)} available models from CosmosAI")
            return available_models
        except Exception as e:
            logger.error(f"Error getting available models: {e}")
            # Return the known supported models as fallback
            return list(self.supported_models)
    
    def test_model(self, test_message: str = "Hi, who are you?") -> bool:
        """
        Test if the model is working correctly.
        
        Args:
            test_message: Message to test with
            
        Returns:
            bool: True if model responds successfully, False otherwise
        """
        try:
            messages = [{"role": "user", "content": test_message}]
            response = self.chat(messages, {"max_tokens": 64, "temperature": 0})
            
            if response and len(response.strip()) > 0:
                logger.info(f"Model {self.model_name} test successful")
                return True
            else:
                logger.warning(f"Model {self.model_name} returned empty response")
                return False
                
        except Exception as e:
            logger.error(f"Model {self.model_name} test failed: {e}")
            return False
    
    @classmethod
    def get_model_info(cls) -> Dict[str, Any]:
        """
        Get information about CosmosAI models.
        
        Returns:
            Dict[str, Any]: Model information
        """
        return {
            "provider": "CosmosAI",
            "base_url": "https://aiplatform.dev51.cbf.dev.paypalinc.com/cosmosai/llm/v1",
            "api_type": "OpenAI-compatible",
            "supported_models": [
                "claude-3-7-sonnet-20250219",
                "claude-3-5-haiku-20241022",
                "claude-sonnet-4-20250514", 
                "llama33-70b",
                "llama31-8b",
                "gpt-4.1",
                "gpt-4o",
                "gpt-4o-mini",
                "qwen25-coder-32b"
            ],
            "features": [
                "chat_completion",
                "structured_output",
                "async_support",
                "multiple_providers"
            ]
        }
