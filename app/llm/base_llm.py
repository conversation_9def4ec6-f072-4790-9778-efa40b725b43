from abc import ABC, abstractmethod
from typing import Optional


class BaseLLM(ABC):
    @abstractmethod
    def chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        raise NotImplementedError

    @abstractmethod
    async def async_chat(
        self,
        messages: list[dict],
        llm_configuration: dict,
        structured_output_schema: Optional[dict] = None,
    ) -> str | dict:
        raise NotImplementedError
