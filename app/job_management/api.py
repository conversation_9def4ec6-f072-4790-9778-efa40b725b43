"""
API endpoints for job management.
"""
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from starlette import status

from app.job_management.database import get_db
from app.job_management.job_service import job_service
from app.job_management.models import JobStatus, JobType, EvaluationJob, JobLog, JobMetrics


# Request/Response models
class JobResponse(BaseModel):
    """Response model for job information."""
    id: int
    job_id: str
    job_type: str
    status: str
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    filename: Optional[str]
    file_size: Optional[int]
    total_segments: Optional[int]
    processed_segments: int
    passed_segments: int
    progress_percentage: float
    current_step: Optional[str]
    model_name: Optional[str]
    temperature: Optional[float]
    use_rag: bool
    rag_collection: Optional[str]
    output_file_path: Optional[str]
    download_url: Optional[str]
    error_message: Optional[str]
    user_id: Optional[str]
    
    class Config:
        from_attributes = True


class JobListResponse(BaseModel):
    """Response model for job list."""
    jobs: List[JobResponse]
    total: int
    limit: int
    offset: int


class JobLogResponse(BaseModel):
    """Response model for job logs."""
    id: int
    job_id: str
    timestamp: datetime
    level: str
    message: str
    details: Optional[dict]
    step: Optional[str]
    segment_index: Optional[int]
    
    class Config:
        from_attributes = True


class JobMetricsResponse(BaseModel):
    """Response model for job metrics."""
    id: int
    job_id: str
    total_duration_seconds: Optional[float]
    segments_per_second: Optional[float]
    peak_memory_mb: Optional[float]
    cpu_usage_percent: Optional[float]
    recorded_at: datetime
    
    class Config:
        from_attributes = True


class JobCancelRequest(BaseModel):
    """Request model for cancelling a job."""
    reason: Optional[str] = None


class JobStatusResponse(BaseModel):
    """Response model for job status check."""
    job_id: str
    status: str
    progress_percentage: float
    current_step: Optional[str]
    processed_segments: int
    total_segments: Optional[int]
    is_active: bool
    can_cancel: bool


# Router
job_api_router = APIRouter(prefix="/jobs", tags=["Job Management"])


@job_api_router.get("/", response_model=JobListResponse)
async def list_jobs(
    status: Optional[str] = Query(None, description="Filter by job status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    db: Session = Depends(get_db)
):
    """
    List evaluation jobs with optional filtering and pagination.
    """
    try:
        # Convert string parameters to enums if provided
        status_enum = None
        if status:
            try:
                status_enum = JobStatus(status.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {status}. Valid values: {[s.value for s in JobStatus]}"
                )
        
        job_type_enum = None
        if job_type:
            try:
                job_type_enum = JobType(job_type.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid job_type: {job_type}. Valid values: {[t.value for t in JobType]}"
                )
        
        # Get jobs
        jobs = job_service.list_jobs(
            db=db,
            status=status_enum,
            job_type=job_type_enum,
            user_id=user_id,
            limit=limit,
            offset=offset
        )
        
        # Get total count for pagination
        total_query = db.query(EvaluationJob)
        if status_enum:
            total_query = total_query.filter(EvaluationJob.status == status_enum)
        if job_type_enum:
            total_query = total_query.filter(EvaluationJob.job_type == job_type_enum)
        if user_id:
            total_query = total_query.filter(EvaluationJob.user_id == user_id)
        
        total = total_query.count()
        
        return JobListResponse(
            jobs=[JobResponse.from_orm(job) for job in jobs],
            total=total,
            limit=limit,
            offset=offset
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing jobs: {str(e)}"
        )


@job_api_router.get("/{job_id}", response_model=JobResponse)
async def get_job(
    job_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific job.
    """
    try:
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        return JobResponse.from_orm(job)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job: {str(e)}"
        )


@job_api_router.get("/{job_id}/status", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    db: Session = Depends(get_db)
):
    """
    Get current status of a job (lightweight endpoint for polling).
    """
    try:
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        is_active = job.status in [JobStatus.PENDING, JobStatus.RUNNING]
        can_cancel = job.status in [JobStatus.PENDING, JobStatus.RUNNING]
        
        return JobStatusResponse(
            job_id=job.job_id,
            status=job.status.value,
            progress_percentage=job.progress_percentage,
            current_step=job.current_step,
            processed_segments=job.processed_segments,
            total_segments=job.total_segments,
            is_active=is_active,
            can_cancel=can_cancel
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job status: {str(e)}"
        )


@job_api_router.post("/{job_id}/cancel")
async def cancel_job(
    job_id: str,
    request: JobCancelRequest,
    db: Session = Depends(get_db)
):
    """
    Cancel a running or pending job.
    """
    try:
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        if job.status not in [JobStatus.PENDING, JobStatus.RUNNING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel job with status {job.status.value}"
            )
        
        success = job_service.cancel_job(db, job_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel job"
            )
        
        # Log cancellation reason if provided
        if request.reason:
            job_service.log_job_event(
                db, job_id, "WARNING", 
                f"Job cancelled by user: {request.reason}"
            )
        
        return {"message": f"Job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cancelling job: {str(e)}"
        )


@job_api_router.get("/{job_id}/logs")
async def get_job_logs(
    job_id: str,
    level: Optional[str] = Query(None, description="Filter by log level"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of logs"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    db: Session = Depends(get_db)
):
    """
    Get logs for a specific job.
    """
    try:
        # Verify job exists
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        # Build query
        query = db.query(JobLog).filter(JobLog.job_id == job_id)
        
        if level:
            query = query.filter(JobLog.level == level.upper())
        
        # Get logs ordered by timestamp (newest first)
        logs = query.order_by(JobLog.timestamp.desc()).offset(offset).limit(limit).all()
        
        # Get total count
        total = query.count()
        
        return {
            "job_id": job_id,
            "logs": [JobLogResponse.from_orm(log) for log in logs],
            "total": total,
            "limit": limit,
            "offset": offset
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job logs: {str(e)}"
        )


@job_api_router.get("/{job_id}/metrics", response_model=JobMetricsResponse)
async def get_job_metrics(
    job_id: str,
    db: Session = Depends(get_db)
):
    """
    Get performance metrics for a specific job.
    """
    try:
        # Verify job exists
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        # Get metrics
        metrics = db.query(JobMetrics).filter(JobMetrics.job_id == job_id).first()
        
        if not metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No metrics found for job {job_id}"
            )
        
        return JobMetricsResponse.from_orm(metrics)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job metrics: {str(e)}"
        )


@job_api_router.delete("/{job_id}")
async def delete_job(
    job_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete a job and all its associated data.
    Note: Only completed, failed, or cancelled jobs can be deleted.
    """
    try:
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        if job.status in [JobStatus.PENDING, JobStatus.RUNNING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete active job. Cancel it first."
            )
        
        # Delete associated logs and metrics
        db.query(JobLog).filter(JobLog.job_id == job_id).delete()
        db.query(JobMetrics).filter(JobMetrics.job_id == job_id).delete()
        
        # Delete the job
        db.delete(job)
        db.commit()
        
        return {"message": f"Job {job_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting job: {str(e)}"
        )


@job_api_router.get("/stats/summary")
async def get_job_stats(
    db: Session = Depends(get_db)
):
    """
    Get summary statistics for all jobs.
    """
    try:
        # Get counts by status
        status_counts = {}
        for status_enum in JobStatus:
            count = db.query(EvaluationJob).filter(EvaluationJob.status == status_enum).count()
            status_counts[status_enum.value] = count

        # Get counts by job type
        type_counts = {}
        for type_enum in JobType:
            count = db.query(EvaluationJob).filter(EvaluationJob.job_type == type_enum).count()
            type_counts[type_enum.value] = count

        # Get active jobs count
        active_count = db.query(EvaluationJob).filter(
            EvaluationJob.status.in_([JobStatus.PENDING, JobStatus.RUNNING])
        ).count()

        # Get total jobs
        total_jobs = db.query(EvaluationJob).count()

        return {
            "total_jobs": total_jobs,
            "active_jobs": active_count,
            "status_counts": status_counts,
            "type_counts": type_counts
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job stats: {str(e)}"
        )


@job_api_router.get("/{job_id}/download")
async def download_job_result(
    job_id: str,
    db: Session = Depends(get_db)
):
    """
    Download the result file for a completed job.
    """
    try:
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )

        if job.status != JobStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job is not completed. Current status: {job.status.value}"
            )

        if not job.output_file_path or not os.path.exists(job.output_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Output file not found"
            )

        from fastapi.responses import FileResponse

        return FileResponse(
            path=job.output_file_path,
            filename=job.filename.replace('.xlsx', '_evaluated.xlsx') if job.filename else 'evaluated_file.xlsx',
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error downloading file: {str(e)}"
        )
