"""
Service for managing evaluation jobs.
"""
import asyncio
import json
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
import psutil
import time

from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from loguru import logger

from app.job_management.models import EvaluationJob, JobLog, JobMetrics, JobStatus, JobType
from app.job_management.database import get_db


class JobService:
    """Service for managing evaluation jobs."""
    
    def __init__(self):
        self.active_jobs = {}  # In-memory tracking of active jobs
    
    def create_job(
        self, 
        db: Session,
        job_type: JobType,
        filename: Optional[str] = None,
        file_size: Optional[int] = None,
        job_config: Optional[Dict] = None,
        user_id: Optional[str] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """
        Create a new evaluation job.
        
        Args:
            db: Database session
            job_type: Type of job
            filename: Name of file being processed
            file_size: Size of file in bytes
            job_config: Job configuration (prompt template, RAG configs, etc.)
            user_id: User identifier
            client_ip: Client IP address
            user_agent: User agent string
            
        Returns:
            str: Job ID
        """
        job_id = str(uuid.uuid4())
        
        job = EvaluationJob(
            job_id=job_id,
            job_type=job_type,
            filename=filename,
            file_size=file_size,
            job_config=job_config,
            user_id=user_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        db.add(job)
        db.commit()
        db.refresh(job)
        
        logger.info(f"Created job {job_id} of type {job_type.value}")
        return job_id
    
    def start_job(self, db: Session, job_id: str) -> bool:
        """
        Mark a job as started.
        
        Args:
            db: Database session
            job_id: Job ID
            
        Returns:
            bool: True if successful
        """
        job = db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
        if not job:
            return False
        
        job.status = JobStatus.RUNNING
        job.started_at = datetime.utcnow()
        db.commit()
        
        # Track in memory
        self.active_jobs[job_id] = {
            "start_time": time.time(),
            "process": None,
            "cancelled": False
        }
        
        self.log_job_event(db, job_id, "INFO", "Job started")
        return True
    
    def update_job_progress(
        self, 
        db: Session, 
        job_id: str,
        processed_segments: Optional[int] = None,
        total_segments: Optional[int] = None,
        passed_segments: Optional[int] = None,
        current_step: Optional[str] = None,
        model_name: Optional[str] = None,
        temperature: Optional[float] = None,
        use_rag: Optional[bool] = None,
        rag_collection: Optional[str] = None
    ) -> bool:
        """
        Update job progress.
        
        Args:
            db: Database session
            job_id: Job ID
            processed_segments: Number of processed segments
            total_segments: Total number of segments
            passed_segments: Number of segments that passed
            current_step: Current processing step
            model_name: LLM model being used
            temperature: Temperature setting
            use_rag: Whether RAG is being used
            rag_collection: RAG collection name
            
        Returns:
            bool: True if successful
        """
        job = db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
        if not job:
            return False
        
        # Update fields if provided
        if processed_segments is not None:
            job.processed_segments = processed_segments
        if total_segments is not None:
            job.total_segments = total_segments
        if passed_segments is not None:
            job.passed_segments = passed_segments
        if current_step is not None:
            job.current_step = current_step
        if model_name is not None:
            job.model_name = model_name
        if temperature is not None:
            job.temperature = temperature
        if use_rag is not None:
            job.use_rag = use_rag
        if rag_collection is not None:
            job.rag_collection = rag_collection
        
        # Calculate progress percentage
        if job.total_segments and job.total_segments > 0:
            job.progress_percentage = (job.processed_segments / job.total_segments) * 100
        
        db.commit()
        return True
    
    def complete_job(
        self, 
        db: Session, 
        job_id: str,
        output_file_path: Optional[str] = None,
        download_url: Optional[str] = None,
        job_results: Optional[Dict] = None
    ) -> bool:
        """
        Mark a job as completed.
        
        Args:
            db: Database session
            job_id: Job ID
            output_file_path: Path to output file
            download_url: URL for downloading result
            job_results: Detailed job results
            
        Returns:
            bool: True if successful
        """
        job = db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
        if not job:
            return False
        
        job.status = JobStatus.COMPLETED
        job.completed_at = datetime.utcnow()
        job.progress_percentage = 100.0
        
        if output_file_path:
            job.output_file_path = output_file_path
        if download_url:
            job.download_url = download_url
        if job_results:
            job.job_results = job_results
        
        db.commit()
        
        # Remove from active jobs
        if job_id in self.active_jobs:
            del self.active_jobs[job_id]
        
        # Record metrics
        self._record_job_metrics(db, job_id)
        
        self.log_job_event(db, job_id, "INFO", "Job completed successfully")
        return True
    
    def fail_job(
        self, 
        db: Session, 
        job_id: str,
        error_message: str,
        error_details: Optional[Dict] = None
    ) -> bool:
        """
        Mark a job as failed.
        
        Args:
            db: Database session
            job_id: Job ID
            error_message: Error message
            error_details: Detailed error information
            
        Returns:
            bool: True if successful
        """
        job = db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
        if not job:
            return False
        
        job.status = JobStatus.FAILED
        job.completed_at = datetime.utcnow()
        job.error_message = error_message
        job.error_details = error_details
        
        db.commit()
        
        # Remove from active jobs
        if job_id in self.active_jobs:
            del self.active_jobs[job_id]
        
        self.log_job_event(db, job_id, "ERROR", f"Job failed: {error_message}")
        return True
    
    def cancel_job(self, db: Session, job_id: str) -> bool:
        """
        Cancel a running job.
        
        Args:
            db: Database session
            job_id: Job ID
            
        Returns:
            bool: True if successful
        """
        job = db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
        if not job:
            return False
        
        if job.status not in [JobStatus.PENDING, JobStatus.RUNNING]:
            return False  # Can't cancel completed/failed jobs
        
        job.status = JobStatus.CANCELLED
        job.completed_at = datetime.utcnow()
        db.commit()
        
        # Mark as cancelled in memory
        if job_id in self.active_jobs:
            self.active_jobs[job_id]["cancelled"] = True
        
        self.log_job_event(db, job_id, "WARNING", "Job cancelled by user")
        return True
    
    def get_job(self, db: Session, job_id: str) -> Optional[EvaluationJob]:
        """
        Get job by ID.
        
        Args:
            db: Database session
            job_id: Job ID
            
        Returns:
            Optional[EvaluationJob]: Job if found
        """
        return db.query(EvaluationJob).filter(EvaluationJob.job_id == job_id).first()
    
    def list_jobs(
        self, 
        db: Session,
        status: Optional[JobStatus] = None,
        job_type: Optional[JobType] = None,
        user_id: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[EvaluationJob]:
        """
        List jobs with optional filtering.
        
        Args:
            db: Database session
            status: Filter by status
            job_type: Filter by job type
            user_id: Filter by user ID
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            List[EvaluationJob]: List of jobs
        """
        query = db.query(EvaluationJob)
        
        if status:
            query = query.filter(EvaluationJob.status == status)
        if job_type:
            query = query.filter(EvaluationJob.job_type == job_type)
        if user_id:
            query = query.filter(EvaluationJob.user_id == user_id)
        
        return query.order_by(desc(EvaluationJob.created_at)).offset(offset).limit(limit).all()
    
    def is_job_cancelled(self, job_id: str) -> bool:
        """
        Check if a job has been cancelled.
        
        Args:
            job_id: Job ID
            
        Returns:
            bool: True if cancelled
        """
        if job_id in self.active_jobs:
            return self.active_jobs[job_id].get("cancelled", False)
        return False
    
    def log_job_event(
        self, 
        db: Session, 
        job_id: str, 
        level: str, 
        message: str,
        details: Optional[Dict] = None,
        step: Optional[str] = None,
        segment_index: Optional[int] = None
    ):
        """
        Log a job event.
        
        Args:
            db: Database session
            job_id: Job ID
            level: Log level (INFO, WARNING, ERROR, DEBUG)
            message: Log message
            details: Additional details
            step: Current step
            segment_index: Current segment index
        """
        log_entry = JobLog(
            job_id=job_id,
            level=level,
            message=message,
            details=details,
            step=step,
            segment_index=segment_index
        )
        
        db.add(log_entry)
        db.commit()
    
    def _record_job_metrics(self, db: Session, job_id: str):
        """
        Record job performance metrics.
        
        Args:
            db: Database session
            job_id: Job ID
        """
        try:
            job = self.get_job(db, job_id)
            if not job or not job.started_at or not job.completed_at:
                return
            
            # Calculate duration
            duration = (job.completed_at - job.started_at).total_seconds()
            
            # Calculate segments per second
            segments_per_second = None
            if job.processed_segments and duration > 0:
                segments_per_second = job.processed_segments / duration
            
            # Get system metrics
            process = psutil.Process()
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            metrics = JobMetrics(
                job_id=job_id,
                total_duration_seconds=duration,
                segments_per_second=segments_per_second,
                peak_memory_mb=memory_info.rss / 1024 / 1024,
                cpu_usage_percent=cpu_percent
            )
            
            db.add(metrics)
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to record metrics for job {job_id}: {e}")


# Global job service instance
job_service = JobService()
