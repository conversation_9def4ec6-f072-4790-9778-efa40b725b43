"""
Job Management Module

This module provides PostgreSQL-based job history management for Excel file evaluation
and other long-running tasks. It includes:

- Database models for jobs, logs, and metrics
- Job service for managing job lifecycle
- API endpoints for job monitoring and control
- Enhanced Excel evaluator with job tracking

Features:
- Create and track evaluation jobs
- Monitor job progress in real-time
- Cancel running jobs
- View job history and logs
- Download completed results
- Performance metrics tracking
"""

from app.job_management.api import job_api_router
from app.job_management.excel_api import excel_job_api_router
from app.job_management.database import db_service, get_db
from app.job_management.job_service import job_service
from app.job_management.models import EvaluationJob, JobLog, JobMetrics, JobStatus, JobType
from app.job_management.excel_evaluator_with_jobs import job_managed_evaluator

__all__ = [
    "job_api_router",
    "excel_job_api_router", 
    "db_service",
    "get_db",
    "job_service",
    "job_managed_evaluator",
    "EvaluationJob",
    "JobLog", 
    "JobMetrics",
    "JobStatus",
    "JobType"
]
