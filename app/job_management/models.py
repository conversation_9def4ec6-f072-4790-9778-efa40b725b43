"""
Database models for job management.
"""
import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, Text, Enum, Float, <PERSON>olean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class JobStatus(enum.Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobType(enum.Enum):
    """Job type enumeration."""
    EXCEL_EVALUATION = "excel_evaluation"
    GLE_WORKFLOW = "gle_workflow"
    SINGLE_SUBMISSION = "single_submission"


class EvaluationJob(Base):
    """Model for tracking evaluation jobs."""
    
    __tablename__ = "evaluation_jobs"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Job identification
    job_id = Column(String(255), unique=True, index=True, nullable=False)
    job_type = Column(Enum(JobType), nullable=False)
    
    # Job status and timing
    status = Column(Enum(JobStatus), default=JobStatus.PENDING, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Job details
    filename = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)  # File size in bytes
    total_segments = Column(Integer, nullable=True)
    processed_segments = Column(Integer, default=0)
    passed_segments = Column(Integer, default=0)
    
    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    current_step = Column(String(255), nullable=True)
    
    # Configuration
    model_name = Column(String(255), nullable=True)
    temperature = Column(Float, nullable=True)
    use_rag = Column(Boolean, default=False)
    rag_collection = Column(String(255), nullable=True)
    
    # Results
    output_file_path = Column(String(1000), nullable=True)
    download_url = Column(String(1000), nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Metadata
    user_id = Column(String(255), nullable=True)
    client_ip = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Additional job-specific data
    job_config = Column(JSON, nullable=True)  # Store prompt template, RAG configs, etc.
    job_results = Column(JSON, nullable=True)  # Store detailed results
    
    def __repr__(self):
        return f"<EvaluationJob(id={self.id}, job_id='{self.job_id}', status='{self.status.value}')>"


class JobLog(Base):
    """Model for storing job execution logs."""
    
    __tablename__ = "job_logs"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key to job
    job_id = Column(String(255), index=True, nullable=False)
    
    # Log details
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # Context
    step = Column(String(255), nullable=True)
    segment_index = Column(Integer, nullable=True)
    
    def __repr__(self):
        return f"<JobLog(id={self.id}, job_id='{self.job_id}', level='{self.level}')>"


class JobMetrics(Base):
    """Model for storing job performance metrics."""
    
    __tablename__ = "job_metrics"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key to job
    job_id = Column(String(255), index=True, nullable=False)
    
    # Timing metrics
    total_duration_seconds = Column(Float, nullable=True)
    llm_call_duration_seconds = Column(Float, nullable=True)
    rag_duration_seconds = Column(Float, nullable=True)
    file_processing_duration_seconds = Column(Float, nullable=True)
    
    # Performance metrics
    segments_per_second = Column(Float, nullable=True)
    average_score = Column(Float, nullable=True)
    score_distribution = Column(JSON, nullable=True)  # {"95-100": 10, "90-94": 5, ...}
    
    # Resource usage
    peak_memory_mb = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    
    # Quality metrics
    error_rate = Column(Float, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Timestamps
    recorded_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<JobMetrics(id={self.id}, job_id='{self.job_id}')>"
