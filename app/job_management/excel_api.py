"""
Enhanced Excel evaluation API with job management.
"""
import os
import tempfile
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel
from starlette import status

from app.job_management.database import get_db
from app.job_management.excel_evaluator_with_jobs import job_managed_evaluator
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig


# Request/Response models
class ExcelEvaluationJobResponse(BaseModel):
    """Response model for Excel evaluation job creation."""
    job_id: str
    message: str
    status_url: str
    download_url: str


class ExcelEvaluationRequest(BaseModel):
    """Request model for Excel evaluation."""
    prompt_template: PromptTemplate
    rag_configs: List[RAGConfig] = []
    user_id: Optional[str] = None


# Router
excel_job_api_router = APIRouter(prefix="/excel-jobs", tags=["Excel Job Management"])


@excel_job_api_router.post("/evaluate", response_model=ExcelEvaluationJobResponse)
async def evaluate_excel_with_job_tracking(
    request: Request,
    file: UploadFile = File(..., description="Excel file to evaluate"),
    prompt_template: str = Form(..., description="JSON string of prompt template"),
    rag_configs: str = Form("[]", description="JSON string of RAG configurations"),
    user_id: Optional[str] = Form(None, description="User identifier"),
    db: Session = Depends(get_db)
):
    """
    Evaluate an Excel file with job tracking.
    
    This endpoint creates a job and returns immediately with a job ID.
    Use the job management endpoints to track progress and download results.
    """
    try:
        # Validate file type
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an Excel file (.xlsx or .xls)"
            )
        
        # Save uploaded file temporarily
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Parse JSON parameters
        import json
        
        try:
            prompt_template_dict = json.loads(prompt_template)
            prompt_template_obj = PromptTemplate(**prompt_template_dict)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid prompt template: {str(e)}"
            )
        
        try:
            rag_configs_list = json.loads(rag_configs)
            rag_configs_obj = [RAGConfig(**config) for config in rag_configs_list]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid RAG configurations: {str(e)}"
            )
        
        # Get client information
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # Start evaluation job
        job_id = await job_managed_evaluator.evaluate_excel_file_with_job_tracking(
            file_path=file_path,
            prompt_template=prompt_template_obj,
            rag_configs=rag_configs_obj,
            user_id=user_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return ExcelEvaluationJobResponse(
            job_id=job_id,
            message="Excel evaluation job started successfully",
            status_url=f"/jobs/{job_id}/status",
            download_url=f"/jobs/{job_id}/download"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting Excel evaluation job: {str(e)}"
        )


@excel_job_api_router.post("/evaluate-json", response_model=ExcelEvaluationJobResponse)
async def evaluate_excel_with_json(
    request: Request,
    file: UploadFile = File(..., description="Excel file to evaluate"),
    evaluation_request: ExcelEvaluationRequest = Depends(),
    db: Session = Depends(get_db)
):
    """
    Evaluate an Excel file with JSON request body (alternative endpoint).
    """
    try:
        # Validate file type
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an Excel file (.xlsx or .xls)"
            )
        
        # Save uploaded file temporarily
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, file.filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Get client information
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # Start evaluation job
        job_id = await job_managed_evaluator.evaluate_excel_file_with_job_tracking(
            file_path=file_path,
            prompt_template=evaluation_request.prompt_template,
            rag_configs=evaluation_request.rag_configs,
            user_id=evaluation_request.user_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return ExcelEvaluationJobResponse(
            job_id=job_id,
            message="Excel evaluation job started successfully",
            status_url=f"/jobs/{job_id}/status",
            download_url=f"/jobs/{job_id}/download"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting Excel evaluation job: {str(e)}"
        )


@excel_job_api_router.get("/active")
async def get_active_excel_jobs(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get all active Excel evaluation jobs.
    """
    try:
        from app.job_management.job_service import job_service
        from app.job_management.models import JobStatus, JobType
        
        # Get active Excel jobs
        active_jobs = job_service.list_jobs(
            db=db,
            status=None,  # We'll filter manually to get both PENDING and RUNNING
            job_type=JobType.EXCEL_EVALUATION,
            user_id=user_id,
            limit=100
        )
        
        # Filter for active statuses
        active_jobs = [
            job for job in active_jobs 
            if job.status in [JobStatus.PENDING, JobStatus.RUNNING]
        ]
        
        return {
            "active_jobs": len(active_jobs),
            "jobs": [
                {
                    "job_id": job.job_id,
                    "filename": job.filename,
                    "status": job.status.value,
                    "progress_percentage": job.progress_percentage,
                    "current_step": job.current_step,
                    "processed_segments": job.processed_segments,
                    "total_segments": job.total_segments,
                    "created_at": job.created_at,
                    "started_at": job.started_at
                }
                for job in active_jobs
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting active Excel jobs: {str(e)}"
        )


@excel_job_api_router.get("/history")
async def get_excel_job_history(
    user_id: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    Get Excel evaluation job history.
    """
    try:
        from app.job_management.job_service import job_service
        from app.job_management.models import JobType
        
        # Get Excel jobs
        jobs = job_service.list_jobs(
            db=db,
            job_type=JobType.EXCEL_EVALUATION,
            user_id=user_id,
            limit=limit,
            offset=offset
        )
        
        # Get total count
        total_query = db.query(EvaluationJob).filter(EvaluationJob.job_type == JobType.EXCEL_EVALUATION)
        if user_id:
            total_query = total_query.filter(EvaluationJob.user_id == user_id)
        total = total_query.count()
        
        return {
            "total": total,
            "limit": limit,
            "offset": offset,
            "jobs": [
                {
                    "job_id": job.job_id,
                    "filename": job.filename,
                    "status": job.status.value,
                    "progress_percentage": job.progress_percentage,
                    "processed_segments": job.processed_segments,
                    "total_segments": job.total_segments,
                    "passed_segments": job.passed_segments,
                    "model_name": job.model_name,
                    "created_at": job.created_at,
                    "started_at": job.started_at,
                    "completed_at": job.completed_at,
                    "error_message": job.error_message,
                    "download_available": job.status.value == "completed" and job.output_file_path is not None
                }
                for job in jobs
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Excel job history: {str(e)}"
        )


@excel_job_api_router.post("/{job_id}/cancel")
async def cancel_excel_job(
    job_id: str,
    reason: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Cancel an active Excel evaluation job.
    """
    try:
        from app.job_management.job_service import job_service
        from app.job_management.models import JobType
        
        # Verify it's an Excel job
        job = job_service.get_job(db, job_id)
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job {job_id} not found"
            )
        
        if job.job_type != JobType.EXCEL_EVALUATION:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Job is not an Excel evaluation job"
            )
        
        # Cancel the job
        success = job_service.cancel_job(db, job_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Job cannot be cancelled (may already be completed or failed)"
            )
        
        # Log cancellation reason
        if reason:
            job_service.log_job_event(
                db, job_id, "WARNING", 
                f"Excel evaluation job cancelled: {reason}"
            )
        
        return {"message": f"Excel evaluation job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cancelling Excel job: {str(e)}"
        )
