"""
Database service for job management.
"""
import os
from typing import Optional

from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from loguru import logger

from app.job_management.models import Base


class DatabaseService:
    """Service for managing database connections and operations."""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and create tables."""
        try:
            # Get database URL from environment or use default
            database_url = os.getenv(
                "DATABASE_URL", 
                "postgresql://postgres:password@localhost:5432/llm_translation_qe"
            )
            
            # Create engine
            self.engine = create_engine(
                database_url,
                poolclass=StaticPool,
                pool_pre_ping=True,
                echo=False  # Set to True for SQL debugging
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False, 
                autoflush=False, 
                bind=self.engine
            )
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            # Fallback to SQLite for development
            self._initialize_sqlite_fallback()
    
    def _initialize_sqlite_fallback(self):
        """Initialize SQLite fallback database."""
        try:
            logger.warning("Using SQLite fallback database")
            sqlite_url = "sqlite:///./job_management.db"
            
            self.engine = create_engine(
                sqlite_url,
                poolclass=StaticPool,
                pool_pre_ping=True,
                echo=False,
                connect_args={"check_same_thread": False}
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False, 
                autoflush=False, 
                bind=self.engine
            )
            
            Base.metadata.create_all(bind=self.engine)
            logger.info("SQLite fallback database initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize SQLite fallback: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get a database session."""
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """Test database connection."""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def close(self):
        """Close database connections."""
        if self.engine:
            self.engine.dispose()


# Global database service instance
db_service = DatabaseService()


def get_db() -> Session:
    """Dependency for getting database session."""
    session = db_service.get_session()
    try:
        yield session
    finally:
        session.close()
