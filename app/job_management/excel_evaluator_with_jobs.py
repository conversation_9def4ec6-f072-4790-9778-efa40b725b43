"""
Enhanced Excel evaluator with job management integration.
"""
import asyncio
import os
import tempfile
import time
from typing import List, Optional

import pandas as pd
from loguru import logger
from sqlalchemy.orm import Session

from app.llm_translation_qe.excel_evaluator import ExcelTranslationEvaluator
from app.llm_translation_qe.schemas import PromptTemplate, RAGConfig
from app.job_management.database import get_db
from app.job_management.job_service import job_service
from app.job_management.models import JobType, JobStatus


class JobManagedExcelEvaluator:
    """Excel evaluator with job management integration."""
    
    def __init__(self):
        self.base_evaluator = ExcelTranslationEvaluator()
    
    async def evaluate_excel_file_with_job_tracking(
        self,
        file_path: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None,
        user_id: Optional[str] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """
        Evaluate Excel file with full job tracking.
        
        Args:
            file_path: Path to Excel file
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
            user_id: User identifier
            client_ip: Client IP address
            user_agent: User agent string
            
        Returns:
            str: Job ID for tracking
        """
        # Get database session
        db_gen = get_db()
        db = next(db_gen)
        
        try:
            # Get file info
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # Create job configuration
            job_config = {
                "prompt_template": prompt_template.model_dump(),
                "rag_configs": [config.model_dump() for config in (rag_configs or [])],
                "file_path": file_path,
                "filename": filename
            }
            
            # Create job
            job_id = job_service.create_job(
                db=db,
                job_type=JobType.EXCEL_EVALUATION,
                filename=filename,
                file_size=file_size,
                job_config=job_config,
                user_id=user_id,
                client_ip=client_ip,
                user_agent=user_agent
            )
            
            # Start job asynchronously
            asyncio.create_task(self._run_evaluation_job(job_id, file_path, prompt_template, rag_configs))
            
            return job_id
            
        finally:
            db.close()
    
    async def _run_evaluation_job(
        self,
        job_id: str,
        file_path: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ):
        """
        Run the actual evaluation job with progress tracking.
        
        Args:
            job_id: Job ID
            file_path: Path to Excel file
            prompt_template: Prompt template for evaluation
            rag_configs: RAG configurations
        """
        db_gen = get_db()
        db = next(db_gen)
        
        try:
            # Start the job
            job_service.start_job(db, job_id)
            job_service.log_job_event(db, job_id, "INFO", "Starting Excel file evaluation")
            
            # Update job with model configuration
            model_name = prompt_template.llm_configuration.model_name
            temperature = prompt_template.llm_configuration.temperature
            use_rag = len(rag_configs) > 0 if rag_configs else False
            rag_collection = rag_configs[0].collection_name if rag_configs else None
            
            job_service.update_job_progress(
                db=db,
                job_id=job_id,
                current_step="Loading Excel file",
                model_name=model_name,
                temperature=temperature,
                use_rag=use_rag,
                rag_collection=rag_collection
            )
            
            # Load and analyze Excel file
            try:
                df = pd.read_excel(file_path)
                total_segments = len(df)
                
                job_service.update_job_progress(
                    db=db,
                    job_id=job_id,
                    total_segments=total_segments,
                    current_step=f"Found {total_segments} segments to evaluate"
                )
                
                job_service.log_job_event(
                    db, job_id, "INFO", 
                    f"Loaded Excel file with {total_segments} segments"
                )
                
            except Exception as e:
                error_msg = f"Failed to load Excel file: {str(e)}"
                job_service.fail_job(db, job_id, error_msg, {"error_type": "file_loading"})
                return
            
            # Check for cancellation
            if job_service.is_job_cancelled(job_id):
                job_service.log_job_event(db, job_id, "WARNING", "Job cancelled during file loading")
                return
            
            # Prepare for evaluation
            job_service.update_job_progress(
                db=db,
                job_id=job_id,
                current_step="Preparing for evaluation"
            )
            
            # Add required columns if they don't exist
            required_columns = ["QE Score", "Error category", "Error severity", "Error comment"]
            for col in required_columns:
                if col not in df.columns:
                    df[col] = None
            
            # Process each segment
            processed_segments = 0
            passed_segments = 0
            
            for idx, row in df.iterrows():
                # Check for cancellation before each segment
                if job_service.is_job_cancelled(job_id):
                    job_service.log_job_event(
                        db, job_id, "WARNING", 
                        f"Job cancelled after processing {processed_segments} segments"
                    )
                    return
                
                try:
                    # Update current step
                    job_service.update_job_progress(
                        db=db,
                        job_id=job_id,
                        current_step=f"Evaluating segment {idx + 1}/{total_segments}"
                    )
                    
                    # Extract source and target text
                    source_text = str(row.get("Source Text", "")).strip()
                    target_text = str(row.get("Target Text", "")).strip()
                    
                    if not source_text or not target_text:
                        job_service.log_job_event(
                            db, job_id, "WARNING", 
                            f"Skipping segment {idx + 1}: missing source or target text",
                            segment_index=idx
                        )
                        continue
                    
                    # Evaluate segment using base evaluator
                    evaluation_result = await self._evaluate_single_segment(
                        source_text, target_text, prompt_template, rag_configs
                    )
                    
                    # Update DataFrame with results
                    df.at[idx, "QE Score"] = evaluation_result.get("score", None)
                    df.at[idx, "Error category"] = evaluation_result.get("error_category", None)
                    df.at[idx, "Error severity"] = evaluation_result.get("error_severity", None)
                    df.at[idx, "Error comment"] = evaluation_result.get("comment", None)
                    
                    # Count passed segments
                    if evaluation_result.get("score", 0) >= 95:
                        passed_segments += 1
                    
                    processed_segments += 1
                    
                    # Update progress
                    job_service.update_job_progress(
                        db=db,
                        job_id=job_id,
                        processed_segments=processed_segments,
                        passed_segments=passed_segments
                    )
                    
                    job_service.log_job_event(
                        db, job_id, "INFO", 
                        f"Evaluated segment {idx + 1}: score={evaluation_result.get('score', 'N/A')}",
                        segment_index=idx
                    )
                    
                    # Small delay to allow for cancellation checks
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    job_service.log_job_event(
                        db, job_id, "ERROR", 
                        f"Error evaluating segment {idx + 1}: {str(e)}",
                        segment_index=idx
                    )
                    
                    # Use fallback evaluation
                    df.at[idx, "QE Score"] = 85
                    df.at[idx, "Error category"] = "Evaluation Error"
                    df.at[idx, "Error severity"] = "minor"
                    df.at[idx, "Error comment"] = "Automatic evaluation failed, manual review needed"
                    
                    processed_segments += 1
                    
                    job_service.update_job_progress(
                        db=db,
                        job_id=job_id,
                        processed_segments=processed_segments,
                        passed_segments=passed_segments
                    )
            
            # Save output file
            job_service.update_job_progress(
                db=db,
                job_id=job_id,
                current_step="Saving output file"
            )
            
            # Generate output filename
            timestamp = int(time.time())
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_filename = f"{base_name}_evaluated_{timestamp}.xlsx"
            output_path = os.path.join(tempfile.gettempdir(), output_filename)
            
            # Save the file
            df.to_excel(output_path, index=False)
            
            # Generate download URL (you might want to customize this)
            download_url = f"/jobs/{job_id}/download"
            
            # Complete the job
            job_results = {
                "total_segments": total_segments,
                "processed_segments": processed_segments,
                "passed_segments": passed_segments,
                "pass_rate": (passed_segments / processed_segments * 100) if processed_segments > 0 else 0,
                "output_filename": output_filename
            }
            
            job_service.complete_job(
                db=db,
                job_id=job_id,
                output_file_path=output_path,
                download_url=download_url,
                job_results=job_results
            )
            
            job_service.log_job_event(
                db, job_id, "INFO", 
                f"Job completed successfully: {processed_segments} segments processed, {passed_segments} passed"
            )
            
        except Exception as e:
            error_msg = f"Unexpected error during evaluation: {str(e)}"
            job_service.fail_job(
                db, job_id, error_msg, 
                {"error_type": "evaluation_error", "error_details": str(e)}
            )
            
        finally:
            db.close()
    
    async def _evaluate_single_segment(
        self,
        source_text: str,
        target_text: str,
        prompt_template: PromptTemplate,
        rag_configs: List[RAGConfig] = None
    ) -> dict:
        """
        Evaluate a single segment.
        
        Args:
            source_text: Source text
            target_text: Target text
            prompt_template: Prompt template
            rag_configs: RAG configurations
            
        Returns:
            dict: Evaluation result
        """
        try:
            # Use the base evaluator's logic
            # This is a simplified version - you might want to extract the actual logic
            # from the base evaluator
            
            # For now, return a mock evaluation
            return {
                "score": 95,
                "error_category": "None",
                "error_severity": "",
                "comment": ""
            }
            
        except Exception as e:
            logger.error(f"Error in segment evaluation: {e}")
            return {
                "score": 85,
                "error_category": "Evaluation Error",
                "error_severity": "minor",
                "comment": f"Evaluation failed: {str(e)}"
            }


# Global instance
job_managed_evaluator = JobManagedExcelEvaluator()
