"""
Quick test for CosmosAI LLM integration.
"""
import os
from app.llm.llm_factory import LLMFactory

def test_factory_creation():
    """Test basic factory creation."""
    print("Testing factory creation...")
    
    test_models = ["gpt-4o-mini", "claude-3-5-haiku-20241022", "llama31-8b"]
    
    for model_name in test_models:
        try:
            print(f"Creating LLM for: {model_name}")
            llm = LLMFactory.build_llm(model_name)
            print(f"✓ Success: {type(llm).__name__}")
            print(f"  Model name: {llm.model_name}")
        except Exception as e:
            print(f"✗ Failed: {e}")
        print()

if __name__ == "__main__":
    test_factory_creation()
