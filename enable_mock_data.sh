#!/bin/bash

# Enable mock data mode for development/testing
export USE_MOCK_DATA=true
export FALLBACK_TO_MOCK_ON_ERROR=true
export FALLBACK_TO_MOCK_ON_NOT_FOUND=true

echo "Mock data mode enabled for development/testing."
echo "The following environment variables have been set:"
echo "USE_MOCK_DATA=true"
echo "FALLBACK_TO_MOCK_ON_ERROR=true"
echo "FALLBACK_TO_MOCK_ON_NOT_FOUND=true"
echo ""
echo "To use these settings, run your application with:"
echo "source enable_mock_data.sh && python -m app.main"
