[tool.poetry]
name = "llm-translation-qe-backend"
version = "0.1.0"
description = "Backend for AI4Tech: LLM-based Translation Quality Evaluation"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.115.8"
uvicorn = "^0.34.0"
vertexai = "^1.71.1"
openai = "^1.63.2"
loguru = "^0.7.3"
google-cloud-bigquery = "^3.29.0"
python-multipart = "^0.0.20"
sqlalchemy = "^2.0.39"
psycopg2-binary = "^2.9.10"
google-genai = "^1.7.0"
pymilvus = "^2.5.6"
pandas = "^2.2.3"
openpyxl = "^3.1.5"

[[tool.poetry.source]]
name = "paypal"
url = "https://artifactory.paypalcorp.com/artifactory/api/pypi/paypal-python-all/simple/"
priority = "primary"

[tool.poetry.group.dev.dependencies]
black = "^25.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
