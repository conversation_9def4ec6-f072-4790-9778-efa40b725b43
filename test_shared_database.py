"""
Test script to verify shared database connection for job management.
"""
from app.job_management.database import db_service
from app.job_management.job_service import job_service
from app.job_management.models import JobType, JobStatus


def test_database_connection():
    """Test database connection and basic operations."""
    print("=" * 50)
    print("TESTING SHARED DATABASE CONNECTION")
    print("=" * 50)
    
    try:
        # Test database connection
        print("1. Testing database connection...")
        connection_ok = db_service.test_connection()
        print(f"   Database connection: {'✓ SUCCESS' if connection_ok else '✗ FAILED'}")
        
        if not connection_ok:
            print("   Cannot proceed with database tests")
            return False
        
        # Test database session
        print("\n2. Testing database session...")
        db_gen = db_service.get_session()
        db = next(db_gen)
        
        try:
            # Test basic query
            result = db.execute("SELECT 1 as test").fetchone()
            print(f"   Database query test: {'✓ SUCCESS' if result else '✗ FAILED'}")
            
            # Test table creation (should already exist)
            from app.job_management.models import EvaluationJob
            job_count = db.query(EvaluationJob).count()
            print(f"   Job table access: ✓ SUCCESS ({job_count} jobs found)")
            
        finally:
            db.close()
        
        # Test job service
        print("\n3. Testing job service...")
        db = next(db_service.get_session())
        
        try:
            # Create a test job
            job_id = job_service.create_job(
                db=db,
                job_type=JobType.EXCEL_EVALUATION,
                filename="test_connection.xlsx",
                file_size=1024,
                job_config={"test": True},
                user_id="test_user"
            )
            print(f"   Job creation: ✓ SUCCESS (Job ID: {job_id})")
            
            # Get the job
            job = job_service.get_job(db, job_id)
            if job:
                print(f"   Job retrieval: ✓ SUCCESS (Status: {job.status.value})")
                
                # Update job status
                success = job_service.start_job(db, job_id)
                print(f"   Job status update: {'✓ SUCCESS' if success else '✗ FAILED'}")
                
                # Cancel the test job
                success = job_service.cancel_job(db, job_id)
                print(f"   Job cancellation: {'✓ SUCCESS' if success else '✗ FAILED'}")
            else:
                print("   Job retrieval: ✗ FAILED")
                
        finally:
            db.close()
        
        print("\n" + "=" * 50)
        print("DATABASE CONNECTION TEST COMPLETED")
        print("=" * 50)
        print("✓ Shared database connection working")
        print("✓ Job management tables accessible")
        print("✓ Job service operations functional")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Database test failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check if PostgreSQL is running at *************")
        print("2. Verify SSL certificates in app/postgres_ssl_cert/")
        print("3. Check network connectivity")
        print("4. System will fall back to SQLite automatically")
        return False


def test_database_info():
    """Display database connection information."""
    print("\n" + "=" * 50)
    print("DATABASE CONNECTION INFO")
    print("=" * 50)
    
    print("Configuration:")
    print("  Database: ai_localization")
    print("  Host: *************")
    print("  Port: 5432 (default)")
    print("  User: postgres")
    print("  SSL: Enabled (with certificates)")
    print("  Fallback: SQLite (./job_management.db)")
    
    print("\nSSL Certificates:")
    print("  Root CA: app/postgres_ssl_cert/server-ca.pem")
    print("  Client Cert: app/postgres_ssl_cert/client-cert.pem")
    print("  Client Key: app/postgres_ssl_cert/client-key.pem")
    
    print("\nTables Created:")
    print("  - evaluation_jobs (main job tracking)")
    print("  - job_logs (detailed execution logs)")
    print("  - job_metrics (performance metrics)")


def test_ssl_certificates():
    """Test SSL certificate availability."""
    print("\n" + "=" * 50)
    print("SSL CERTIFICATE CHECK")
    print("=" * 50)
    
    import os
    
    cert_files = [
        "app/postgres_ssl_cert/server-ca.pem",
        "app/postgres_ssl_cert/client-cert.pem", 
        "app/postgres_ssl_cert/client-key.pem"
    ]
    
    all_certs_exist = True
    
    for cert_file in cert_files:
        exists = os.path.exists(cert_file)
        status = "✓ EXISTS" if exists else "✗ MISSING"
        print(f"  {cert_file}: {status}")
        
        if not exists:
            all_certs_exist = False
    
    if all_certs_exist:
        print("\n✓ All SSL certificates found")
        print("  Will attempt SSL connection to PostgreSQL")
    else:
        print("\n⚠ Some SSL certificates missing")
        print("  Will attempt connection without SSL")
        print("  Or fall back to SQLite if connection fails")


def main():
    """Run all database tests."""
    print("JOB MANAGEMENT SHARED DATABASE TESTS")
    print("=" * 60)
    
    test_database_info()
    test_ssl_certificates()
    
    success = test_database_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ SHARED DATABASE INTEGRATION SUCCESSFUL")
        print("  Job management is ready to use")
        print("  Database tables created and accessible")
        print("  Job service operations working")
    else:
        print("⚠ DATABASE CONNECTION ISSUES")
        print("  Check troubleshooting steps above")
        print("  System will use SQLite fallback")
    print("=" * 60)


if __name__ == "__main__":
    main()
