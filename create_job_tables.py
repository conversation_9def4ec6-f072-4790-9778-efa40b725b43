"""
<PERSON><PERSON><PERSON> to create job management tables in the shared database.
"""
from app.job_management.database import db_service
from app.job_management.models import Base
from loguru import logger


def create_job_tables():
    """Create job management tables in the shared database."""
    print("=" * 50)
    print("CREATING JOB MANAGEMENT TABLES")
    print("=" * 50)
    
    try:
        # Initialize database service (this will create tables)
        print("Initializing database service...")
        
        if not db_service.engine:
            print("✗ Database engine not initialized")
            return False
        
        # Create all tables
        print("Creating job management tables...")
        Base.metadata.create_all(bind=db_service.engine)
        
        # Test table creation
        print("Testing table creation...")
        with db_service.get_session() as session:
            # Test each table
            tables_to_test = [
                ("evaluation_jobs", "SELECT COUNT(*) FROM evaluation_jobs"),
                ("job_logs", "SELECT COUNT(*) FROM job_logs"),
                ("job_metrics", "SELECT COUNT(*) FROM job_metrics")
            ]
            
            for table_name, query in tables_to_test:
                try:
                    result = session.execute(query).scalar()
                    print(f"  ✓ Table '{table_name}': {result} rows")
                except Exception as e:
                    print(f"  ✗ Table '{table_name}': Error - {e}")
                    return False
        
        print("\n" + "=" * 50)
        print("✓ JOB MANAGEMENT TABLES CREATED SUCCESSFULLY")
        print("=" * 50)
        print("Tables created in shared database:")
        print("  - evaluation_jobs (main job tracking)")
        print("  - job_logs (execution logs)")
        print("  - job_metrics (performance data)")
        print("\nDatabase: ai_localization at 10.183.153.65")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating job tables: {e}")
        print("\nTroubleshooting:")
        print("1. Check PostgreSQL connection")
        print("2. Verify SSL certificates")
        print("3. Check database permissions")
        return False


def show_table_info():
    """Show information about the created tables."""
    print("\n" + "=" * 50)
    print("JOB MANAGEMENT TABLE SCHEMA")
    print("=" * 50)
    
    print("1. evaluation_jobs:")
    print("   - Primary job tracking table")
    print("   - Stores job status, progress, configuration")
    print("   - Links to logs and metrics")
    
    print("\n2. job_logs:")
    print("   - Detailed execution logs")
    print("   - Timestamped log entries")
    print("   - Different log levels (INFO, WARNING, ERROR)")
    
    print("\n3. job_metrics:")
    print("   - Performance metrics")
    print("   - Timing and resource usage data")
    print("   - Job completion statistics")
    
    print("\nIndexes:")
    print("   - job_id (for fast job lookups)")
    print("   - status (for filtering active jobs)")
    print("   - created_at (for chronological ordering)")


def main():
    """Main function to create tables and show info."""
    print("JOB MANAGEMENT TABLE CREATION")
    print("=" * 60)
    
    success = create_job_tables()
    
    if success:
        show_table_info()
        
        print("\n" + "=" * 60)
        print("SETUP COMPLETE")
        print("=" * 60)
        print("✓ Job management system ready to use")
        print("✓ Shared database integration active")
        print("✓ Tables created and accessible")
        
        print("\nNext steps:")
        print("1. Start the FastAPI application")
        print("2. Test job management endpoints")
        print("3. Upload Excel files for evaluation")
    else:
        print("\n" + "=" * 60)
        print("SETUP FAILED")
        print("=" * 60)
        print("✗ Could not create job management tables")
        print("  Check database connection and permissions")
        print("  System will fall back to SQLite if needed")


if __name__ == "__main__":
    main()
