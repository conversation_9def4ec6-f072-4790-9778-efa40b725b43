"""
Usage example for the new CosmosAI LLM integration.
"""
import asyncio
import os
from app.llm.llm_factory import LLMFactory


async def basic_usage_example():
    """Basic usage example."""
    print("=" * 50)
    print("BASIC COSMOSAI USAGE EXAMPLE")
    print("=" * 50)
    
    # Set your API key (replace with actual key)
    os.environ["COSMOSAI_API_KEY"] = "<LLM_API_KEY>"
    
    # Create LLM instance using factory
    model_name = "gpt-4o-mini"  # You can use any supported model
    llm = LLMFactory.build_llm(model_name)
    
    print(f"Created LLM instance for model: {model_name}")
    
    # Prepare messages
    messages = [
        {"role": "user", "content": "Hello! Can you help me with translation quality evaluation?"}
    ]
    
    # LLM configuration
    llm_config = {
        "temperature": 0.7,
        "max_tokens": 150,
        "top_p": 0.9
    }
    
    try:
        # Sync chat
        print("\nSync chat response:")
        response = llm.chat(messages, llm_config)
        print(response)
        
        # Async chat
        print("\nAsync chat response:")
        async_response = await llm.async_chat(messages, llm_config)
        print(async_response)
        
    except Exception as e:
        print(f"Error: {e}")


async def translation_evaluation_example():
    """Translation quality evaluation example."""
    print("\n" + "=" * 50)
    print("TRANSLATION EVALUATION EXAMPLE")
    print("=" * 50)
    
    # Set your API key
    os.environ["COSMOSAI_API_KEY"] = "<LLM_API_KEY>"
    
    # Use a model good for evaluation
    model_name = "claude-3-5-haiku-********"
    llm = LLMFactory.build_llm(model_name)
    
    # Translation evaluation prompt
    messages = [
        {
            "role": "system",
            "content": "You are a professional translation quality evaluator. Evaluate translations and provide scores from 1-100."
        },
        {
            "role": "user", 
            "content": """
            Please evaluate this translation:
            
            Source (English): "Click here to access your PayPal account"
            Target (Spanish): "Haga clic aquí para acceder a su cuenta de PayPal"
            
            Provide a quality score (1-100) and brief explanation.
            """
        }
    ]
    
    llm_config = {
        "temperature": 0.0,  # Low temperature for consistent evaluation
        "max_tokens": 200
    }
    
    try:
        response = await llm.async_chat(messages, llm_config)
        print(f"Evaluation result:\n{response}")
        
    except Exception as e:
        print(f"Error: {e}")


async def structured_output_example():
    """Structured output example for translation evaluation."""
    print("\n" + "=" * 50)
    print("STRUCTURED OUTPUT EXAMPLE")
    print("=" * 50)
    
    # Set your API key
    os.environ["COSMOSAI_API_KEY"] = "<LLM_API_KEY>"
    
    # Use GPT model for structured output
    model_name = "gpt-4o"
    llm = LLMFactory.build_llm(model_name)
    
    messages = [
        {
            "role": "system",
            "content": "You are a translation quality evaluator. Respond with structured JSON."
        },
        {
            "role": "user",
            "content": """
            Evaluate this translation:
            Source: "Welcome to PayPal"
            Target: "Bienvenido a PayPal"
            
            Provide your evaluation in the specified JSON format.
            """
        }
    ]
    
    # Define structured output schema
    schema = {
        "type": "object",
        "properties": {
            "score": {
                "type": "number",
                "description": "Quality score from 1-100"
            },
            "error_category": {
                "type": "string",
                "description": "Error category or 'None' if no errors"
            },
            "error_severity": {
                "type": "string",
                "description": "Error severity: minor, major, critical, or empty if no error"
            },
            "comment": {
                "type": "string",
                "description": "Brief explanation"
            }
        },
        "required": ["score", "error_category", "error_severity", "comment"]
    }
    
    llm_config = {
        "temperature": 0.0,
        "max_tokens": 150
    }
    
    try:
        response = llm.chat(messages, llm_config, schema)
        print("Structured evaluation result:")
        print(f"Type: {type(response)}")
        
        if isinstance(response, dict):
            print("Parsed fields:")
            for key, value in response.items():
                print(f"  {key}: {value}")
        else:
            print(f"Response: {response}")
            
    except Exception as e:
        print(f"Error: {e}")


async def multi_model_comparison():
    """Compare responses from different models."""
    print("\n" + "=" * 50)
    print("MULTI-MODEL COMPARISON")
    print("=" * 50)
    
    # Set your API key
    os.environ["COSMOSAI_API_KEY"] = "<LLM_API_KEY>"
    
    # Test different models
    models_to_test = [
        "gpt-4o-mini",
        "claude-3-5-haiku-********", 
        "llama31-8b"
    ]
    
    prompt = "Explain what makes a good translation in one sentence."
    messages = [{"role": "user", "content": prompt}]
    
    llm_config = {
        "temperature": 0.5,
        "max_tokens": 100
    }
    
    print(f"Prompt: {prompt}\n")
    
    for model_name in models_to_test:
        try:
            print(f"Model: {model_name}")
            llm = LLMFactory.build_llm(model_name)
            response = await llm.async_chat(messages, llm_config)
            print(f"Response: {response}\n")
            
        except Exception as e:
            print(f"Error with {model_name}: {e}\n")


def list_available_models():
    """List all available models."""
    print("=" * 50)
    print("AVAILABLE MODELS")
    print("=" * 50)
    
    from app.llm import llm_models_list
    
    print("All supported models:")
    for i, model in enumerate(llm_models_list, 1):
        print(f"{i:2d}. {model}")
    
    print(f"\nTotal: {len(llm_models_list)} models")


async def main():
    """Run all examples."""
    print("COSMOSAI LLM USAGE EXAMPLES")
    print("=" * 60)
    
    # List available models
    list_available_models()
    
    # Note about API key
    print("\n" + "!" * 60)
    print("IMPORTANT: Replace '<LLM_API_KEY>' with your actual API key")
    print("You can set it as an environment variable: COSMOSAI_API_KEY")
    print("!" * 60)
    
    # Run examples (uncomment to test with real API key)
    # await basic_usage_example()
    # await translation_evaluation_example()
    # await structured_output_example()
    # await multi_model_comparison()
    
    print("\nTo run the examples, uncomment the function calls in main() and set your API key.")


if __name__ == "__main__":
    asyncio.run(main())
