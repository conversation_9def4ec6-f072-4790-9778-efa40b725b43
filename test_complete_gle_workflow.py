"""
Test script for the complete GLE workflow including upload and completion.
"""
import asyncio
import os
from datetime import datetime

from app.gle.workflow import GLEWorkflow
from app.gle.service import GLEService
from app.llm_translation_qe.schemas import (
    PromptTemplate, 
    TemplateMessage, 
    LLMConfiguration, 
    OutputSchemaItem,
    RAGConfig
)


async def test_complete_workflow():
    """Test the complete GLE workflow with upload and completion."""
    
    print("=" * 60)
    print("COMPLETE GLE WORKFLOW TEST")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    
    # Create a sample prompt template for translation quality evaluation
    template_messages = [
        TemplateMessage(
            role="system",
            content="You are a professional translation quality evaluator. Your task is to evaluate the quality of "
                   "translation from source language to target language. Provide a score from 1 to 100, identify "
                   "any error categories, and determine the severity of errors."
        ),
        TemplateMessage(
            role="user",
            content="Please evaluate the following translation:\n\n"
                   "Source text: {{source_text}}\n\n"
                   "Target text: {{target_text}}\n\n"
                   "Provide the following in your evaluation:\n"
                   "1. A quality score between 1 and 100, with 1 being very poor and 100 being excellent. "
                   "If translation is error-free, score should be 95 or above. "
                   "If there are one or more errors, even if minor, score should be 94 or below.\n"
                   "2. An error category (if an error is found - otherwise 'None')\n"
                   "3. An error severity (minor, major or critical - blank if no error)\n"
                   "4. A comment explaining what the error is (blank if no error)\n\n"
                   "Please refer to the retrieval document: {{glossary}}"
        )
    ]
    
    output_schema = [
        OutputSchemaItem(
            name="score",
            type="number",
            description="Translation quality score from 1 to 100",
            required=True
        ),
        OutputSchemaItem(
            name="error_category",
            type="string",
            description="Primary error category found in the translation (or 'None' if no errors)",
            required=True
        ),
        OutputSchemaItem(
            name="error_severity",
            type="string",
            description="Error severity: 'minor', 'major', 'critical', or empty string if no error",
            required=True
        ),
        OutputSchemaItem(
            name="comment",
            type="string",
            description="Brief explanation of the error, or empty string if no error",
            required=True
        )
    ]
    
    llm_configuration = LLMConfiguration(
        model_name="google/gemini-2.0-flash",
        temperature=0.0,
        top_p=0.5
    )
    
    prompt_template = PromptTemplate(
        template_messages=template_messages,
        template_variables=[],
        llm_configuration=llm_configuration,
        output_schema=output_schema
    )
    
    # Create RAG configs (optional)
    rag_configs = [
        RAGConfig(
            collection_name="paypal_glossary",
            texts=[],  # Will be populated automatically
            limit=5,
            placeholder_key="glossary"
        )
    ]
    
    # Create workflow instance
    workflow = GLEWorkflow()
    
    try:
        # Run the complete workflow
        results = await workflow.run_complete_workflow(
            prompt_template=prompt_template,
            rag_configs=rag_configs,
            output_dir="/tmp/gle_complete_test_output"
        )
        
        print("\n" + "="*60)
        print("COMPLETE WORKFLOW RESULTS")
        print("="*60)
        print(f"Success: {results['success']}")
        print(f"Submissions processed: {results['submissions_processed']}")
        print(f"Files processed: {results['files_processed']}")
        print(f"Files passed: {results['files_passed']}")
        print(f"Output files: {len(results['output_files'])}")
        
        if results['output_files']:
            print("\nOutput files:")
            for file_path in results['output_files']:
                print(f"  - {file_path}")
        
        if results['errors']:
            print(f"\nErrors ({len(results['errors'])}):")
            for error in results['errors']:
                print(f"  - {error}")
        
        print("="*60)
        
        return results
        
    except Exception as e:
        print(f"Error running complete GLE workflow: {e}")
        return None


async def test_individual_operations():
    """Test individual GLE operations."""
    
    print("\n" + "=" * 60)
    print("INDIVIDUAL OPERATIONS TEST")
    print("=" * 60)
    
    gle_service = GLEService()
    
    try:
        # Test authentication
        print("1. Testing authentication...")
        auth_success = await gle_service.authenticate()
        print(f"   Authentication: {'Success' if auth_success else 'Failed'}")
        
        if not auth_success:
            print("   Cannot proceed without authentication")
            return
        
        # Test getting claimable submissions
        print("\n2. Getting claimable submissions...")
        submissions = await gle_service.get_claimable_submissions()
        print(f"   Found {len(submissions)} claimable submissions")
        
        if submissions:
            submission = submissions[0]
            submission_id = submission['submissionId']
            print(f"   Testing with submission {submission_id}: {submission['submissionName']}")
            
            # Test getting target IDs
            print(f"\n3. Getting target IDs for submission {submission_id}...")
            languages = submission.get('languages', [])
            if languages:
                target_lang = languages[0].get('targetLanguageCode')
                print(f"   Testing with target language: {target_lang}")
                
                target_ids = await gle_service.get_target_ids(submission_id, target_lang)
                print(f"   Found target IDs: {target_ids}")
                
                # Note: We won't test upload and completion here as they require actual files
                print(f"\n4. Upload and completion operations would be tested with actual files")
                print(f"   Upload endpoint: /gle/upload")
                print(f"   Complete phase endpoint: /gle/complete-phase")
            else:
                print("   No languages found in submission")
        else:
            print("   No submissions available for testing")
        
    except Exception as e:
        print(f"Error testing individual operations: {e}")


async def test_api_endpoints():
    """Test the API endpoints."""
    
    print("\n" + "=" * 60)
    print("API ENDPOINTS TEST")
    print("=" * 60)
    
    print("Available API endpoints:")
    print("1. POST /gle/workflow/run - Run complete workflow")
    print("2. POST /gle/submission/process - Process single submission")
    print("3. GET /gle/submissions/claimable - Get claimable submissions")
    print("4. POST /gle/upload - Upload processed file")
    print("5. POST /gle/complete-phase - Complete API_QE phase")
    print("6. GET /gle/submissions/{id}/targets - Get target IDs")
    print("7. GET /gle/files/download/{path} - Download processed file")
    print("8. GET /gle/health - Health check")
    
    print("\nExample API calls:")
    print("""
    # Get claimable submissions
    curl -X GET "http://localhost:8080/gle/submissions/claimable"
    
    # Upload a file
    curl -X POST "http://localhost:8080/gle/upload" \\
      -H "Content-Type: application/json" \\
      -d '{
        "submission_id": 4942,
        "file_path": "/path/to/processed/file.txlf",
        "original_filename": "original_file.txlf"
      }'
    
    # Complete phase
    curl -X POST "http://localhost:8080/gle/complete-phase" \\
      -H "Content-Type: application/json" \\
      -d '{
        "submission_id": 4942,
        "target_language": "de"
      }'
    
    # Health check
    curl -X GET "http://localhost:8080/gle/health"
    """)


def print_workflow_summary():
    """Print a summary of the complete workflow."""
    
    print("\n" + "=" * 60)
    print("COMPLETE GLE WORKFLOW SUMMARY")
    print("=" * 60)
    
    print("""
    The complete GLE workflow now includes all 7 steps:
    
    1. AUTHENTICATION
       - OAuth2 authentication with GLE system
       - Automatic token refresh
    
    2. GET CLAIMABLE SUBMISSIONS
       - Retrieve submissions ready for API_QE
       - Filter for LANGUAGE claim level
    
    3. CLAIM SUBMISSIONS
       - Claim submissions and languages
       - Handle multiple languages per submission
    
    4. DOWNLOAD FILES
       - Request download with status checking
       - Extract TXLF files from ZIP
       - Preserve original filenames (CRITICAL)
    
    5. PROCESS TXLF FILES
       - Evaluate mt-suggestion segments
       - Add gs4tr:score and gs4tr:locked attributes
       - Add gs4tr:qe-evaluation="pass" for files with all scores ≥95
    
    6. UPLOAD PROCESSED FILES ⭐ NEW
       - Upload files back to GLE using original filenames
       - Multipart form upload with extractArchive=false
       - Preserve exact filename from GLE
    
    7. COMPLETE API_QE PHASE ⭐ NEW
       - Get target IDs for submission and language
       - Complete phase with API_QE_Autoskip transition
       - Handle multiple target languages
    
    Key Features:
    - End-to-end automation
    - Robust error handling
    - Filename preservation (critical for GLE)
    - Comprehensive logging
    - API endpoints for manual operations
    """)


async def main():
    """Run all tests."""
    print("COMPLETE GLE INTEGRATION TESTS")
    print("=" * 60)
    
    # Print workflow summary
    print_workflow_summary()
    
    # Test individual operations
    await test_individual_operations()
    
    # Show API endpoints
    await test_api_endpoints()
    
    # Test complete workflow (uncomment to run with real data)
    # await test_complete_workflow()
    
    print("\n" + "=" * 60)
    print("TESTS COMPLETED")
    print("=" * 60)
    print("To run the complete workflow, uncomment the test_complete_workflow() call")
    print("Make sure you have proper GLE credentials configured")


if __name__ == "__main__":
    asyncio.run(main())
